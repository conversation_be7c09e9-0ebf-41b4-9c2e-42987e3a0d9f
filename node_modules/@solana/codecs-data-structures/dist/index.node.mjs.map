{"version": 3, "sources": ["../src/assertions.ts", "../src/utils.ts", "../src/array.ts", "../src/bit-array.ts", "../src/boolean.ts", "../src/bytes.ts", "../../codecs-strings/src/base16.ts", "../src/constant.ts", "../src/tuple.ts", "../src/union.ts", "../src/discriminated-union.ts", "../src/enum-helpers.ts", "../src/enum.ts", "../src/hidden-prefix.ts", "../src/hidden-suffix.ts", "../src/map.ts", "../src/unit.ts", "../src/nullable.ts", "../src/set.ts", "../src/struct.ts"], "names": ["newOffset", "createEncoder", "createDecoder", "combineCodec", "SolanaError", "getEncodedSize", "isFixedSize", "getU8Encoder", "transformEncoder", "getU8Decoder", "transformDecoder", "containsBytes"], "mappings": ";;;;;AAGO,SAAS,gCAAA,CACZ,gBACA,EAAA,QAAA,EACA,MACF,EAAA;AACE,EAAA,IAAI,aAAa,MAAQ,EAAA;AACrB,IAAM,MAAA,IAAI,YAAY,6CAA+C,EAAA;AAAA,MACjE,MAAA;AAAA,MACA,gBAAA;AAAA,MACA,QAAA;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;ACDO,SAAS,cAAc,KAAyC,EAAA;AACnE,EAAA,OAAO,KAAM,CAAA,MAAA;AAAA,IACT,CAAC,GAAK,EAAA,IAAA,KAAU,GAAQ,KAAA,IAAA,IAAQ,IAAS,KAAA,IAAA,GAAO,IAAO,GAAA,IAAA,CAAK,GAAI,CAAA,GAAA,EAAK,IAAI,CAAA;AAAA,IACzE,CAAA;AAAA,GACJ,CAAA;AACJ,CAAA;AAEO,SAAS,cAAc,KAAyC,EAAA;AACnE,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAU,KAAA,GAAA,KAAQ,IAAQ,IAAA,IAAA,KAAS,IAAO,GAAA,IAAA,GAAO,GAAM,GAAA,IAAA,EAAO,CAAkB,CAAA,CAAA;AAC9G,CAAA;AAEO,SAAS,aAAa,KAAoE,EAAA;AAC7F,EAAA,OAAO,WAAY,CAAA,KAAK,CAAI,GAAA,KAAA,CAAM,SAAY,GAAA,IAAA,CAAA;AAClD,CAAA;AAEO,SAAS,WAAW,KAAoE,EAAA;AAC3F,EAAA,OAAO,YAAY,KAAK,CAAA,GAAI,KAAM,CAAA,SAAA,GAAa,MAAM,OAAW,IAAA,IAAA,CAAA;AACpE,CAAA;;;ACiCO,SAAS,eACZ,CAAA,IAAA,EACA,MAA0C,GAAA,EAC1B,EAAA;AAChB,EAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,IAAQ,aAAc,EAAA,CAAA;AAC1C,EAAA,MAAM,SAAY,GAAA,yBAAA,CAA0B,IAAM,EAAA,YAAA,CAAa,IAAI,CAAC,CAAA,CAAA;AACpE,EAAA,MAAM,UAAU,yBAA0B,CAAA,IAAA,EAAM,UAAW,CAAA,IAAI,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAErE,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,SAAA,KAAc,IACZ,GAAA,EAAE,WACF,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KAAmB,KAAA;AAClC,QAAM,MAAA,UAAA,GAAa,OAAO,IAAS,KAAA,QAAA,GAAW,eAAe,KAAM,CAAA,MAAA,EAAQ,IAAI,CAAI,GAAA,CAAA,CAAA;AACnF,QAAA,OAAO,UAAa,GAAA,CAAC,GAAG,KAAK,EAAE,MAAO,CAAA,CAAC,GAAK,EAAA,KAAA,KAAU,GAAM,GAAA,cAAA,CAAe,KAAO,EAAA,IAAI,GAAG,CAAC,CAAA,CAAA;AAAA,OAC9F;AAAA,MACA,OAAA;AAAA,KACJ;AAAA,IACN,KAAO,EAAA,CAAC,KAAgB,EAAA,KAAA,EAAO,MAAW,KAAA;AACtC,MAAI,IAAA,OAAO,SAAS,QAAU,EAAA;AAC1B,QAAiC,gCAAA,CAAA,OAAA,EAAS,IAAM,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,OAChE;AACA,MAAI,IAAA,OAAO,SAAS,QAAU,EAAA;AAC1B,QAAA,MAAA,GAAS,IAAK,CAAA,KAAA,CAAM,KAAM,CAAA,MAAA,EAAQ,OAAO,MAAM,CAAA,CAAA;AAAA,OACnD;AACA,MAAA,KAAA,CAAM,QAAQ,CAAS,KAAA,KAAA;AACnB,QAAA,MAAA,GAAS,IAAK,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAAA,OAC3C,CAAA,CAAA;AACD,MAAO,OAAA,MAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAoBO,SAAS,eAAqB,CAAA,IAAA,EAAoB,MAA0C,GAAA,EAAoB,EAAA;AACnH,EAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,IAAQ,aAAc,EAAA,CAAA;AAC1C,EAAM,MAAA,QAAA,GAAW,aAAa,IAAI,CAAA,CAAA;AAClC,EAAM,MAAA,SAAA,GAAY,yBAA0B,CAAA,IAAA,EAAM,QAAQ,CAAA,CAAA;AAC1D,EAAA,MAAM,UAAU,yBAA0B,CAAA,IAAA,EAAM,UAAW,CAAA,IAAI,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAErE,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,SAAU,EAAA,GAAI,EAAE,OAAQ,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,QAAe,EAAC,CAAA;AACtB,MAAI,IAAA,OAAO,SAAS,QAAY,IAAA,KAAA,CAAM,MAAM,MAAM,CAAA,CAAE,WAAW,CAAG,EAAA;AAC9D,QAAO,OAAA,CAAC,OAAO,MAAM,CAAA,CAAA;AAAA,OACzB;AAEA,MAAA,IAAI,SAAS,WAAa,EAAA;AACtB,QAAO,OAAA,MAAA,GAAS,MAAM,MAAQ,EAAA;AAC1B,UAAA,MAAM,CAAC,KAAOA,EAAAA,UAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AAClD,UAASA,MAAAA,GAAAA,UAAAA,CAAAA;AACT,UAAA,KAAA,CAAM,KAAK,KAAK,CAAA,CAAA;AAAA,SACpB;AACA,QAAO,OAAA,CAAC,OAAO,MAAM,CAAA,CAAA;AAAA,OACzB;AAEA,MAAA,MAAM,CAAC,YAAA,EAAc,SAAS,CAAA,GAAI,OAAO,IAAS,KAAA,QAAA,GAAW,CAAC,IAAA,EAAM,MAAM,CAAA,GAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AACrG,MAAS,MAAA,GAAA,SAAA,CAAA;AACT,MAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,YAAA,EAAc,KAAK,CAAG,EAAA;AACtC,QAAA,MAAM,CAAC,KAAOA,EAAAA,UAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AAClD,QAASA,MAAAA,GAAAA,UAAAA,CAAAA;AACT,QAAA,KAAA,CAAM,KAAK,KAAK,CAAA,CAAA;AAAA,OACpB;AACA,MAAO,OAAA,CAAC,OAAO,MAAM,CAAA,CAAA;AAAA,KACzB;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAoBO,SAAS,aACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACnB,EAAA;AACrB,EAAO,OAAA,YAAA,CAAa,gBAAgB,IAAM,EAAA,MAAgB,GAAG,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAC,CAAA,CAAA;AACxG,CAAA;AAEA,SAAS,yBAAA,CAA0B,MAAqC,QAAwC,EAAA;AAC5G,EAAI,IAAA,OAAO,IAAS,KAAA,QAAA,EAAiB,OAAA,IAAA,CAAA;AACrC,EAAI,IAAA,IAAA,KAAS,GAAU,OAAA,CAAA,CAAA;AACvB,EAAO,OAAA,QAAA,KAAa,IAAO,GAAA,IAAA,GAAO,QAAW,GAAA,IAAA,CAAA;AACjD,CAAA;AC1JO,SAAS,kBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACN,EAAA;AAClC,EAAA,MAAM,eAAoC,OAAO,MAAA,KAAW,YAAY,EAAE,QAAA,EAAU,QAAW,GAAA,MAAA,CAAA;AAC/F,EAAM,MAAA,QAAA,GAAW,aAAa,QAAY,IAAA,KAAA,CAAA;AAC1C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,IAAA;AAAA,IACX,KAAA,CAAM,KAAkB,EAAA,KAAA,EAAO,MAAQ,EAAA;AACnC,MAAA,MAAM,aAAuB,EAAC,CAAA;AAE9B,MAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,IAAA,EAAM,KAAK,CAAG,EAAA;AAC9B,QAAA,IAAI,IAAO,GAAA,CAAA,CAAA;AACX,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,CAAA,EAAG,KAAK,CAAG,EAAA;AAC3B,UAAA,MAAM,UAAU,MAAO,CAAA,KAAA,CAAM,IAAI,CAAI,GAAA,CAAC,KAAK,CAAC,CAAA,CAAA;AAC5C,UAAQ,IAAA,IAAA,OAAA,KAAY,QAAW,GAAA,CAAA,GAAI,CAAI,GAAA,CAAA,CAAA,CAAA;AAAA,SAC3C;AACA,QAAA,IAAI,QAAU,EAAA;AACV,UAAA,UAAA,CAAW,QAAQ,IAAI,CAAA,CAAA;AAAA,SACpB,MAAA;AACH,UAAA,UAAA,CAAW,KAAK,IAAI,CAAA,CAAA;AAAA,SACxB;AAAA,OACJ;AAEA,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA,CAAA;AAC5B,MAAO,OAAA,IAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,kBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACN,EAAA;AAClC,EAAA,MAAM,eAAoC,OAAO,MAAA,KAAW,YAAY,EAAE,QAAA,EAAU,QAAW,GAAA,MAAA,CAAA;AAC/F,EAAM,MAAA,QAAA,GAAW,aAAa,QAAY,IAAA,KAAA,CAAA;AAC1C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,IAAA;AAAA,IACX,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,MAAsC,qCAAA,CAAA,UAAA,EAAY,IAAM,EAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AACrE,MAAA,MAAM,WAAsB,EAAC,CAAA;AAC7B,MAAA,IAAI,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,MAAA,EAAQ,SAAS,IAAI,CAAA,CAAA;AAC7C,MAAQ,KAAA,GAAA,QAAA,GAAW,KAAM,CAAA,OAAA,EAAY,GAAA,KAAA,CAAA;AAErC,MAAA,KAAA,CAAM,QAAQ,CAAQ,IAAA,KAAA;AAClB,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,CAAA,EAAG,KAAK,CAAG,EAAA;AAC3B,UAAA,IAAI,QAAU,EAAA;AACV,YAAA,QAAA,CAAS,IAAK,CAAA,OAAA,CAAQ,IAAO,GAAA,CAAC,CAAC,CAAA,CAAA;AAC/B,YAAS,IAAA,KAAA,CAAA,CAAA;AAAA,WACN,MAAA;AACH,YAAA,QAAA,CAAS,IAAK,CAAA,OAAA,CAAQ,IAAO,GAAA,GAAW,CAAC,CAAA,CAAA;AACzC,YAAS,IAAA,KAAA,CAAA,CAAA;AAAA,WACb;AAAA,SACJ;AAAA,OACH,CAAA,CAAA;AAED,MAAO,OAAA,CAAC,QAAU,EAAA,MAAA,GAAS,IAAI,CAAA,CAAA;AAAA,KACnC;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,gBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACG,EAAA;AAC3C,EAAOC,OAAAA,YAAAA,CAAa,mBAAmB,IAAM,EAAA,MAAM,GAAG,kBAAmB,CAAA,IAAA,EAAM,MAAM,CAAC,CAAA,CAAA;AAC1F,CAAA;AC3DO,SAAS,iBAAA,CAAkB,MAA4C,GAAA,EAAsB,EAAA;AAChG,EAAO,OAAA,gBAAA,CAAiB,OAAO,IAAQ,IAAA,YAAA,IAAgB,CAAC,KAAA,KAAoB,KAAQ,GAAA,CAAA,GAAI,CAAE,CAAA,CAAA;AAC9F,CAAA;AAYO,SAAS,iBAAA,CAAkB,MAA4C,GAAA,EAAsB,EAAA;AAChG,EAAO,OAAA,gBAAA,CAAiB,MAAO,CAAA,IAAA,IAAQ,YAAa,EAAA,EAAG,CAAC,KAAoC,KAAA,MAAA,CAAO,KAAK,CAAA,KAAM,CAAC,CAAA,CAAA;AACnH,CAAA;AAYO,SAAS,eAAA,CAAgB,MAA0C,GAAA,EAAoB,EAAA;AAC1F,EAAA,OAAOA,aAAa,iBAAkB,CAAA,MAAM,CAAG,EAAA,iBAAA,CAAkB,MAAM,CAAC,CAAA,CAAA;AAC5E,CAAA;AC1DO,SAAS,eAAwE,GAAA;AACpF,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,WAAS,KAAM,CAAA,MAAA;AAAA,IACjC,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAC7B,MAAM,KAAA,CAAA,GAAA,CAAI,OAAO,MAAM,CAAA,CAAA;AACvB,MAAA,OAAO,SAAS,KAAM,CAAA,MAAA,CAAA;AAAA,KAC1B;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,eAA2D,GAAA;AACvE,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAM,MAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAChC,MAAA,OAAO,CAAC,KAAA,EAAO,MAAS,GAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,KACxC;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,aAAwF,GAAA;AACpG,EAAA,OAAOC,YAAa,CAAA,eAAA,EAAmB,EAAA,eAAA,EAAiB,CAAA,CAAA;AAC5D,CAAA;ACsBa,IAAA,gBAAA,GAAmB,MAC5BD,aAAc,CAAA;AACV,EAAA,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,IAAA,MAAM,QAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,CAAK,SAAS,EAAE,CAAA,CAAE,SAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA,CAAA;AAC7F,IAAA,OAAA,CAAC,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,GAAA;AAEnC,CAAC,CAAA,CAAA;AC7DE,SAAS,mBACZ,QAC2C,EAAA;AAC3C,EAAA,OAAOD,aAAc,CAAA;AAAA,IACjB,WAAW,QAAS,CAAA,MAAA;AAAA,IACpB,KAAO,EAAA,CAAC,CAAG,EAAA,KAAA,EAAO,MAAW,KAAA;AACzB,MAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA,CAAA;AAC1B,MAAA,OAAO,SAAS,QAAS,CAAA,MAAA,CAAA;AAAA,KAC7B;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,mBACZ,QAC2C,EAAA;AAC3C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,WAAW,QAAS,CAAA,MAAA;AAAA,IACpB,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAA,MAAM,SAAS,gBAAiB,EAAA,CAAA;AAChC,MAAA,IAAI,CAAC,aAAA,CAAc,KAAO,EAAA,QAAA,EAAU,MAAM,CAAG,EAAA;AACzC,QAAM,MAAA,IAAIE,YAAY,sCAAwC,EAAA;AAAA,UAC1D,QAAA;AAAA,UACA,IAAM,EAAA,KAAA;AAAA,UACN,WAAA,EAAa,MAAO,CAAA,MAAA,CAAO,QAAQ,CAAA;AAAA,UACnC,OAAA,EAAS,MAAO,CAAA,MAAA,CAAO,KAAK,CAAA;AAAA,UAC5B,MAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACL;AACA,MAAA,OAAO,CAAC,KAAA,CAAA,EAAW,MAAS,GAAA,QAAA,CAAS,MAAM,CAAA,CAAA;AAAA,KAC/C;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAOO,SAAS,iBACZ,QAC+C,EAAA;AAC/C,EAAA,OAAOD,aAAa,kBAAmB,CAAA,QAAQ,CAAG,EAAA,kBAAA,CAAmB,QAAQ,CAAC,CAAA,CAAA;AAClF,CAAA;ACrBO,SAAS,gBACZ,KACwC,EAAA;AAExC,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,KAAM,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA,CAAA;AACvD,EAAA,MAAM,UAAU,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAExD,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,GAAI,cAAc,IACZ,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KACf,KAAA,KAAA,CAAM,IAAI,CAAC,IAAA,EAAM,UAAUI,cAAe,CAAA,KAAA,CAAM,KAAK,CAAG,EAAA,IAAI,CAAC,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,GAAA,KAAQ,GAAM,GAAA,GAAA,EAAK,CAAC,CAAA;AAAA,MACpG,OAAA;AAAA,KACJ,GACA,EAAE,SAAU,EAAA;AAAA,IAClB,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAO,MAAW,KAAA;AACpC,MAAA,gCAAA,CAAiC,OAAS,EAAA,KAAA,CAAM,MAAQ,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACpE,MAAM,KAAA,CAAA,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAU,KAAA;AAC3B,QAAA,MAAA,GAAS,KAAK,KAAM,CAAA,KAAA,CAAM,KAAK,CAAA,EAAG,OAAO,MAAM,CAAA,CAAA;AAAA,OAClD,CAAA,CAAA;AACD,MAAO,OAAA,MAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAcO,SAAS,gBACZ,KACwC,EAAA;AAExC,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,KAAM,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA,CAAA;AACvD,EAAA,MAAM,UAAU,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAExD,EAAA,OAAOH,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,OAAQ,EAAA,GAAI,EAAE,SAAU,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,SAAS,EAAC,CAAA;AAChB,MAAA,KAAA,CAAM,QAAQ,CAAQ,IAAA,KAAA;AAClB,QAAA,MAAM,CAAC,QAAU,EAAA,SAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AACrD,QAAA,MAAA,CAAO,KAAK,QAAQ,CAAA,CAAA;AACpB,QAAS,MAAA,GAAA,SAAA,CAAA;AAAA,OACZ,CAAA,CAAA;AACD,MAAO,OAAA,CAAC,QAAQ,MAAM,CAAA,CAAA;AAAA,KAC1B;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAgBO,SAAS,cACZ,KACyG,EAAA;AACzG,EAAOC,OAAAA,YAAAA;AAAA,IACH,gBAAgB,KAAK,CAAA;AAAA,IACrB,gBAAgB,KAAK,CAAA;AAAA,GACzB,CAAA;AACJ,CAAA;ACxFO,SAAS,eAAA,CACZ,UACA,iBAC8C,EAAA;AAE9C,EAAM,MAAA,SAAA,GAAY,kBAAkB,QAAQ,CAAA,CAAA;AAC5C,EAAA,MAAM,KAAiC,GAAA,CAAC,OAAS,EAAA,KAAA,EAAO,MAAW,KAAA;AAC/D,IAAM,MAAA,KAAA,GAAQ,kBAAkB,OAAO,CAAA,CAAA;AACvC,IAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA,CAAA;AACvC,IAAA,OAAO,SAAS,KAAK,CAAA,CAAE,KAAM,CAAA,OAAA,EAAS,OAAO,MAAM,CAAA,CAAA;AAAA,GACvD,CAAA;AAEA,EAAA,IAAI,cAAc,IAAM,EAAA;AACpB,IAAA,OAAOF,aAAc,CAAA,EAAE,SAAW,EAAA,KAAA,EAAO,CAAA,CAAA;AAAA,GAC7C;AAEA,EAAM,MAAA,OAAA,GAAU,gBAAgB,QAAQ,CAAA,CAAA;AACxC,EAAA,OAAOA,aAAc,CAAA;AAAA,IACjB,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,KAAY,EAAC;AAAA,IACtC,kBAAkB,CAAW,OAAA,KAAA;AACzB,MAAM,MAAA,KAAA,GAAQ,kBAAkB,OAAO,CAAA,CAAA;AACvC,MAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA,CAAA;AACvC,MAAA,OAAOI,cAAe,CAAA,OAAA,EAAS,QAAS,CAAA,KAAK,CAAC,CAAA,CAAA;AAAA,KAClD;AAAA,IACA,KAAA;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,eAAA,CACZ,UACA,iBAC8C,EAAA;AAE9C,EAAM,MAAA,SAAA,GAAY,kBAAkB,QAAQ,CAAA,CAAA;AAC5C,EAAM,MAAA,IAAA,GAA6B,CAAC,KAAA,EAAO,MAAW,KAAA;AAClD,IAAM,MAAA,KAAA,GAAQ,iBAAkB,CAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAC7C,IAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA,CAAA;AACvC,IAAA,OAAO,QAAS,CAAA,KAAK,CAAE,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AAAA,GAC7C,CAAA;AAEA,EAAA,IAAI,cAAc,IAAM,EAAA;AACpB,IAAA,OAAOH,aAAc,CAAA,EAAE,SAAW,EAAA,IAAA,EAAM,CAAA,CAAA;AAAA,GAC5C;AAEA,EAAM,MAAA,OAAA,GAAU,gBAAgB,QAAQ,CAAA,CAAA;AACxC,EAAOA,OAAAA,aAAAA,CAAc,EAAE,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,EAAY,GAAA,EAAK,EAAA,IAAA,EAAM,CAAA,CAAA;AAC3E,CAAA;AASO,SAAS,aAAA,CACZ,QACA,EAAA,iBAAA,EACA,iBAIF,EAAA;AACE,EAAOC,OAAAA,YAAAA;AAAA,IACH,eAAA,CAAgB,UAAU,iBAAiB,CAAA;AAAA,IAC3C,eAAA,CAAgB,UAAU,iBAAiB,CAAA;AAAA,GAG/C,CAAA;AACJ,CAAA;AAEA,SAAS,uBAAA,CAAwB,UAA8B,KAAe,EAAA;AAC1E,EAAA,IAAI,OAAO,QAAA,CAAS,KAAK,CAAA,KAAM,WAAa,EAAA;AACxC,IAAM,MAAA,IAAIC,YAAY,gDAAkD,EAAA;AAAA,MACpE,QAAA,EAAU,SAAS,MAAS,GAAA,CAAA;AAAA,MAC5B,QAAU,EAAA,CAAA;AAAA,MACV,OAAS,EAAA,KAAA;AAAA,KACZ,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;AAEA,SAAS,kBAAoF,QAAqB,EAAA;AAC9G,EAAI,IAAA,QAAA,CAAS,MAAW,KAAA,CAAA,EAAU,OAAA,CAAA,CAAA;AAClC,EAAA,IAAI,CAACE,WAAY,CAAA,QAAA,CAAS,CAAC,CAAC,GAAU,OAAA,IAAA,CAAA;AACtC,EAAM,MAAA,WAAA,GAAc,QAAS,CAAA,CAAC,CAAE,CAAA,SAAA,CAAA;AAChC,EAAM,MAAA,iBAAA,GAAoB,SAAS,KAAM,CAAA,CAAA,OAAA,KAAWA,YAAY,OAAO,CAAA,IAAK,OAAQ,CAAA,SAAA,KAAc,WAAW,CAAA,CAAA;AAC7G,EAAA,OAAO,oBAAoB,WAAc,GAAA,IAAA,CAAA;AAC7C,CAAA;AAEA,SAAS,gBAAkF,QAAqB,EAAA;AAC5G,EAAA,OAAO,cAAc,QAAS,CAAA,GAAA,CAAI,aAAW,UAAW,CAAA,OAAO,CAAC,CAAC,CAAA,CAAA;AACrE,CAAA;;;ACjBO,SAAS,4BAIZ,CAAA,QAAA,EACA,MAA+E,GAAA,EACT,EAAA;AAEtE,EAAM,MAAA,qBAAA,GAAyB,OAAO,aAAiB,IAAA,QAAA,CAAA;AACvD,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQC,YAAa,EAAA,CAAA;AAC3C,EAAO,OAAA,eAAA;AAAA,IACH,QAAS,CAAA,GAAA;AAAA,MAAI,CAAC,GAAG,OAAO,CAAG,EAAA,KAAA,KACvBC,iBAAiB,eAAgB,CAAA,CAAC,MAAQ,EAAA,OAAO,CAAC,CAAG,EAAA,CAAC,UAAkC,CAAC,KAAA,EAAO,KAAK,CAAC,CAAA;AAAA,KAC1G;AAAA,IACA,CAAS,KAAA,KAAA,uBAAA,CAAwB,QAAU,EAAA,KAAA,CAAM,qBAAqB,CAAC,CAAA;AAAA,GAC3E,CAAA;AACJ,CAAA;AAQO,SAAS,4BAIZ,CAAA,QAAA,EACA,MAA+E,GAAA,EACT,EAAA;AACtE,EAAM,MAAA,qBAAA,GAAwB,OAAO,aAAiB,IAAA,QAAA,CAAA;AACtD,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQC,YAAa,EAAA,CAAA;AAC3C,EAAO,OAAA,eAAA;AAAA,IACH,QAAS,CAAA,GAAA;AAAA,MAAI,CAAC,CAAC,aAAA,EAAe,OAAO,CAAA,KACjCC,iBAAiB,eAAgB,CAAA,CAAC,MAAQ,EAAA,OAAO,CAAC,CAAG,EAAA,CAAC,GAAG,KAAK,CAAO,MAAA;AAAA,QACjE,CAAC,qBAAqB,GAAG,aAAA;AAAA,QACzB,GAAG,KAAA;AAAA,OACL,CAAA,CAAA;AAAA,KACN;AAAA,IACA,CAAC,KAAO,EAAA,MAAA,KAAW,MAAO,CAAA,MAAA,CAAO,KAAK,KAAO,EAAA,MAAM,CAAE,CAAA,CAAC,CAAC,CAAA;AAAA,GAC3D,CAAA;AACJ,CAAA;AAQO,SAAS,0BAIZ,CAAA,QAAA,EACA,MAA6E,GAAA,EAK/E,EAAA;AACE,EAAOP,OAAAA,YAAAA;AAAA,IACH,4BAAA,CAA6B,UAAU,MAAM,CAAA;AAAA,IAC7C,4BAAA,CAA6B,UAAU,MAAM,CAAA;AAAA,GAIjD,CAAA;AACJ,CAAA;AAEA,SAAS,uBAAA,CACL,UACA,kBACF,EAAA;AACE,EAAM,MAAA,aAAA,GAAgB,SAAS,SAAU,CAAA,CAAC,CAAC,GAAG,CAAA,KAAM,uBAAuB,GAAG,CAAA,CAAA;AAC9E,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACnB,IAAM,MAAA,IAAIC,YAAY,yDAA2D,EAAA;AAAA,MAC7E,KAAO,EAAA,kBAAA;AAAA,MACP,UAAU,QAAS,CAAA,GAAA,CAAI,CAAC,CAAC,GAAG,MAAM,GAAG,CAAA;AAAA,KACxC,CAAA,CAAA;AAAA,GACL;AACA,EAAO,OAAA,aAAA,CAAA;AACX,CAAA;AAGO,IAAM,kBAAqB,GAAA,6BAAA;AAG3B,IAAM,kBAAqB,GAAA,6BAAA;AAG3B,IAAM,gBAAmB,GAAA,2BAAA;;;AC1KzB,SAAS,aAAa,WAA+B,EAAA;AACxD,EAAA,MAAM,eAAkB,GAAA;AAAA,IACpB,GAAG,IAAI,GAAI,CAAA,MAAA,CAAO,MAAO,CAAA,WAAW,CAAE,CAAA,MAAA,CAAO,CAAK,CAAA,KAAA,OAAO,CAAM,KAAA,QAAQ,CAAa,CAAA;AAAA,IACtF,IAAK,EAAA,CAAA;AACP,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,WAAA,CAAY,MAAO,CAAA,OAAA,CAAQ,WAAW,CAAE,CAAA,KAAA,CAAM,eAAgB,CAAA,MAAM,CAAC,CAAA,CAAA;AAI/F,EAAM,MAAA,QAAA,GAAW,MAAO,CAAA,IAAA,CAAK,UAAU,CAAA,CAAA;AACvC,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,MAAA,CAAO,UAAU,CAAA,CAAA;AAC3C,EAAA,MAAM,YAAyB,GAAA;AAAA,IAC3B,mBAAG,IAAI,GAAI,CAAA,CAAC,GAAG,QAAU,EAAA,GAAG,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,CAAM,KAAA,QAAQ,CAAC,CAAC,CAAA;AAAA,GAC7F,CAAA;AAEA,EAAA,OAAO,EAAE,QAAA,EAAU,UAAY,EAAA,UAAA,EAAY,iBAAiB,YAAa,EAAA,CAAA;AAC7E,CAAA;AAEO,SAAS,uBAAwB,CAAA;AAAA,EACpC,QAAA;AAAA,EACA,UAAA;AAAA,EACA,OAAA;AACJ,CAIW,EAAA;AACP,EAAA,MAAM,UAAa,GAAA,aAAA,CAAc,UAAY,EAAA,CAAA,KAAA,KAAS,UAAU,OAAO,CAAA,CAAA;AACvE,EAAI,IAAA,UAAA,IAAc,GAAU,OAAA,UAAA,CAAA;AAC5B,EAAA,OAAO,QAAS,CAAA,SAAA,CAAU,CAAO,GAAA,KAAA,GAAA,KAAQ,OAAO,CAAA,CAAA;AACpD,CAAA;AAEO,SAAS,6BAA8B,CAAA;AAAA,EAC1C,aAAA;AAAA,EACA,QAAA;AAAA,EACA,UAAA;AAAA,EACA,yBAAA;AACJ,CAKW,EAAA;AACP,EAAA,IAAI,CAAC,yBAA2B,EAAA;AAC5B,IAAA,OAAO,aAAiB,IAAA,CAAA,IAAK,aAAgB,GAAA,QAAA,CAAS,SAAS,aAAgB,GAAA,CAAA,CAAA,CAAA;AAAA,GACnF;AACA,EAAA,OAAO,aAAc,CAAA,UAAA,EAAY,CAAS,KAAA,KAAA,KAAA,KAAU,aAAa,CAAA,CAAA;AACrE,CAAA;AAEA,SAAS,aAAA,CAAiB,OAAiB,SAAmE,EAAA;AAC1G,EAAA,IAAI,IAAI,KAAM,CAAA,MAAA,CAAA;AACd,EAAA,OAAO,CAAK,EAAA,EAAA;AACR,IAAA,IAAI,UAAU,KAAM,CAAA,CAAC,GAAG,CAAG,EAAA,KAAK,GAAU,OAAA,CAAA,CAAA;AAAA,GAC9C;AACA,EAAO,OAAA,CAAA,CAAA,CAAA;AACX,CAAA;AAEO,SAAS,sBAAsB,MAA0B,EAAA;AAC5D,EAAI,IAAA,MAAA,CAAO,MAAW,KAAA,CAAA,EAAU,OAAA,EAAA,CAAA;AAChC,EAAA,IAAI,QAA0B,CAAC,MAAA,CAAO,CAAC,CAAG,EAAA,MAAA,CAAO,CAAC,CAAC,CAAA,CAAA;AACnD,EAAA,MAAM,SAAmB,EAAC,CAAA;AAC1B,EAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,MAAA,CAAO,QAAQ,KAAS,EAAA,EAAA;AAChD,IAAM,MAAA,KAAA,GAAQ,OAAO,KAAK,CAAA,CAAA;AAC1B,IAAA,IAAI,KAAM,CAAA,CAAC,CAAI,GAAA,CAAA,KAAM,KAAO,EAAA;AACxB,MAAA,KAAA,CAAM,CAAC,CAAI,GAAA,KAAA,CAAA;AAAA,KACR,MAAA;AACH,MAAO,MAAA,CAAA,IAAA,CAAK,MAAM,CAAC,CAAA,KAAM,MAAM,CAAC,CAAA,GAAI,GAAG,KAAM,CAAA,CAAC,CAAC,CAAK,CAAA,GAAA,CAAA,EAAG,MAAM,CAAC,CAAC,IAAI,KAAM,CAAA,CAAC,CAAC,CAAE,CAAA,CAAA,CAAA;AAC7E,MAAQ,KAAA,GAAA,CAAC,OAAO,KAAK,CAAA,CAAA;AAAA,KACzB;AAAA,GACJ;AACA,EAAO,MAAA,CAAA,IAAA,CAAK,MAAM,CAAC,CAAA,KAAM,MAAM,CAAC,CAAA,GAAI,GAAG,KAAM,CAAA,CAAC,CAAC,CAAK,CAAA,GAAA,CAAA,EAAG,MAAM,CAAC,CAAC,IAAI,KAAM,CAAA,CAAC,CAAC,CAAE,CAAA,CAAA,CAAA;AAC7E,EAAO,OAAA,MAAA,CAAO,KAAK,IAAI,CAAA,CAAA;AAC3B,CAAA;;;AC5BO,SAAS,cACZ,CAAA,WAAA,EACA,MAAyC,GAAA,EACd,EAAA;AAC3B,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQG,YAAa,EAAA,CAAA;AAC3C,EAAM,MAAA,yBAAA,GAA4B,OAAO,yBAA6B,IAAA,KAAA,CAAA;AACtE,EAAA,MAAM,EAAE,QAAU,EAAA,UAAA,EAAY,iBAAiB,YAAa,EAAA,GAAI,aAAa,WAAW,CAAA,CAAA;AACxF,EAAA,IAAI,6BAA6B,UAAW,CAAA,IAAA,CAAK,WAAS,OAAO,KAAA,KAAU,QAAQ,CAAG,EAAA;AAClF,IAAM,MAAA,IAAIH,YAAY,sEAAwE,EAAA;AAAA,MAC1F,cAAc,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,MAAM,QAAQ,CAAA;AAAA,KAC5E,CAAA,CAAA;AAAA,GACL;AACA,EAAOI,OAAAA,gBAAAA,CAAiB,MAAQ,EAAA,CAAC,OAAwC,KAAA;AACrE,IAAA,MAAM,QAAQ,uBAAwB,CAAA,EAAE,QAAU,EAAA,UAAA,EAAY,SAAS,CAAA,CAAA;AACvE,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,IAAIJ,YAAY,0CAA4C,EAAA;AAAA,QAC9D,wBAAA,EAA0B,sBAAsB,eAAe,CAAA;AAAA,QAC/D,eAAA;AAAA,QACA,YAAA;AAAA,QACA,OAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACL;AACA,IAAO,OAAA,yBAAA,GAA6B,UAAW,CAAA,KAAK,CAAe,GAAA,KAAA,CAAA;AAAA,GACtE,CAAA,CAAA;AACL,CAAA;AAoBO,SAAS,cACZ,CAAA,WAAA,EACA,MAAyC,GAAA,EAChB,EAAA;AACzB,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQK,YAAa,EAAA,CAAA;AAC3C,EAAM,MAAA,yBAAA,GAA4B,OAAO,yBAA6B,IAAA,KAAA,CAAA;AACtE,EAAA,MAAM,EAAE,QAAU,EAAA,UAAA,EAAY,eAAgB,EAAA,GAAI,aAAa,WAAW,CAAA,CAAA;AAC1E,EAAA,IAAI,6BAA6B,UAAW,CAAA,IAAA,CAAK,WAAS,OAAO,KAAA,KAAU,QAAQ,CAAG,EAAA;AAClF,IAAM,MAAA,IAAIL,YAAY,sEAAwE,EAAA;AAAA,MAC1F,cAAc,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,MAAM,QAAQ,CAAA;AAAA,KAC5E,CAAA,CAAA;AAAA,GACL;AACA,EAAOM,OAAAA,gBAAAA,CAAiB,MAAQ,EAAA,CAAC,KAA6C,KAAA;AAC1E,IAAM,MAAA,aAAA,GAAgB,OAAO,KAAK,CAAA,CAAA;AAClC,IAAA,MAAM,QAAQ,6BAA8B,CAAA;AAAA,MACxC,aAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,yBAAA;AAAA,KACH,CAAA,CAAA;AACD,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,mBAAA,GAAsB,yBACtB,GAAA,eAAA,GACA,CAAC,GAAG,MAAM,QAAS,CAAA,MAAM,CAAE,CAAA,IAAA,EAAM,CAAA,CAAA;AACvC,MAAM,MAAA,IAAIN,YAAY,qDAAuD,EAAA;AAAA,QACzE,aAAA;AAAA,QACA,4BAAA,EAA8B,sBAAsB,mBAAmB,CAAA;AAAA,QACvE,mBAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACL;AACA,IAAA,OAAO,WAAW,KAAK,CAAA,CAAA;AAAA,GAC1B,CAAA,CAAA;AACL,CAAA;AAoBO,SAAS,YACZ,CAAA,WAAA,EACA,MAAuC,GAAA,EACI,EAAA;AAC3C,EAAOD,OAAAA,YAAAA,CAAa,eAAe,WAAa,EAAA,MAAM,GAAG,cAAe,CAAA,WAAA,EAAa,MAAM,CAAC,CAAA,CAAA;AAChG,CAAA;AAGO,IAAM,oBAAuB,GAAA,eAAA;AAG7B,IAAM,oBAAuB,GAAA,eAAA;AAG7B,IAAM,kBAAqB,GAAA,aAAA;AC5J3B,SAAS,sBAAA,CACZ,SACA,gBACc,EAAA;AACd,EAAOK,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,GAAG,gBAAA,EAAkB,OAAO,CAAC,CAAA;AAAA,IAC9C,CAAC,UAAiB,CAAC,GAAG,iBAAiB,GAAI,CAAA,MAAM,KAAS,CAAA,CAAA,EAAG,KAAK,CAAA;AAAA,GACtE,CAAA;AACJ,CAAA;AAcO,SAAS,sBAAA,CACZ,SACA,gBACY,EAAA;AACZ,EAAOE,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,GAAG,gBAAA,EAAkB,OAAO,CAAC,CAAA;AAAA,IAC9C,CAAS,KAAA,KAAA,KAAA,CAAM,KAAM,CAAA,MAAA,GAAS,CAAC,CAAA;AAAA,GACnC,CAAA;AACJ,CAAA;AAcO,SAAS,oBAAA,CACZ,OACA,cACiB,EAAA;AACjB,EAAOP,OAAAA,YAAAA,CAAa,uBAAuB,KAAO,EAAA,cAAc,GAAG,sBAAuB,CAAA,KAAA,EAAO,cAAc,CAAC,CAAA,CAAA;AACpH,CAAA;ACjDO,SAAS,sBAAA,CACZ,SACA,gBACc,EAAA;AACd,EAAOK,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,OAAS,EAAA,GAAG,gBAAgB,CAAC,CAAA;AAAA,IAC9C,CAAC,UAAiB,CAAC,KAAA,EAAO,GAAG,gBAAiB,CAAA,GAAA,CAAI,MAAM,KAAA,CAAS,CAAC,CAAA;AAAA,GACtE,CAAA;AACJ,CAAA;AAcO,SAAS,sBAAA,CACZ,SACA,gBACY,EAAA;AACZ,EAAOE,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,OAAS,EAAA,GAAG,gBAAgB,CAAC,CAAA;AAAA,IAC9C,CAAA,KAAA,KAAS,MAAM,CAAC,CAAA;AAAA,GACpB,CAAA;AACJ,CAAA;AAcO,SAAS,oBAAA,CACZ,OACA,cACiB,EAAA;AACjB,EAAOP,OAAAA,YAAAA,CAAa,uBAAuB,KAAO,EAAA,cAAc,GAAG,sBAAuB,CAAA,KAAA,EAAO,cAAc,CAAC,CAAA,CAAA;AACpH,CAAA;AC5BO,SAAS,aACZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAwC,EACN,EAAA;AAClC,EAAOK,OAAAA,gBAAAA;AAAA,IACH,gBAAgB,eAAgB,CAAA,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB,CAAA;AAAA,IAC/D,CAAC,GAA6D,KAAA,CAAC,GAAG,GAAA,CAAI,SAAS,CAAA;AAAA,GACnF,CAAA;AACJ,CAAA;AAwBO,SAAS,aACZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAwC,EACV,EAAA;AAC9B,EAAOE,OAAAA,gBAAAA;AAAA,IACH,gBAAgB,eAAgB,CAAA,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB,CAAA;AAAA,IAC/D,CAAC,OAAA,KAAyD,IAAI,GAAA,CAAI,OAAO,CAAA;AAAA,GAC7E,CAAA;AACJ,CAAA;AAuCO,SAAS,WAMZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAsC,EACiB,EAAA;AACvD,EAAOP,OAAAA,YAAAA,CAAa,aAAc,CAAA,GAAA,EAAK,KAAO,EAAA,MAAgB,GAAG,aAAc,CAAA,GAAA,EAAK,KAAO,EAAA,MAAgB,CAAC,CAAA,CAAA;AAChH,CAAA;ACjIO,SAAS,cAA4C,GAAA;AACxD,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,CAAA;AAAA,IACX,KAAO,EAAA,CAAC,MAAQ,EAAA,MAAA,EAAQ,MAAW,KAAA,MAAA;AAAA,GACtC,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,cAA4C,GAAA;AACxD,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,CAAA;AAAA,IACX,MAAM,CAAC,MAAA,EAAyC,MAAW,KAAA,CAAC,QAAW,MAAM,CAAA;AAAA,GAChF,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,YAA8C,GAAA;AAC1D,EAAA,OAAOC,YAAa,CAAA,cAAA,EAAkB,EAAA,cAAA,EAAgB,CAAA,CAAA;AAC1D,CAAA;;;AC4DO,SAAS,kBACZ,CAAA,IAAA,EACA,MAA6C,GAAA,EACxB,EAAA;AACrB,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOK,gBAAiB,CAAA,cAAA,EAAkB,EAAA,CAAC,aAAsB,KAAS,CAAA,CAAA,CAAA;AAAA,KAC9E;AACA,IAAA,OAAO,kBAAkB,EAAE,IAAA,EAAM,OAAO,MAAUD,IAAAA,YAAAA,IAAgB,CAAA,CAAA;AAAA,GACnE,GAAA,CAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAA,iBAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,OAAO,cAAe,CAAA,cAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA,CAAA;AAAA,KAC1D;AACA,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAO,cAAe,EAAA,CAAA;AAAA,KAC1B;AACA,IAAO,OAAA,kBAAA,CAAmB,OAAO,SAAS,CAAA,CAAA;AAAA,GAC3C,GAAA,CAAA;AAEH,EAAO,OAAA,eAAA;AAAA,IACH;AAAA,MACIC,gBAAAA,CAAiB,gBAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,CAAC,MAAkC,KAAA;AAAA,QACtF,KAAA;AAAA,QACA,KAAA,CAAA;AAAA,OACH,CAAA;AAAA,MACDA,gBAAiB,CAAA,eAAA,CAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAG,EAAA,CAAC,KAAmC,KAAA,CAAC,IAAM,EAAA,KAAK,CAAC,CAAA;AAAA,KACvG;AAAA,IACA,CAAA,OAAA,KAAW,MAAO,CAAA,OAAA,KAAY,IAAI,CAAA;AAAA,GACtC,CAAA;AACJ,CAAA;AAwBO,SAAS,kBACZ,CAAA,IAAA,EACA,MAA6C,GAAA,EAC1B,EAAA;AACnB,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOE,gBAAiB,CAAA,cAAA,EAAkB,EAAA,MAAM,KAAK,CAAA,CAAA;AAAA,KACzD;AACA,IAAA,OAAO,kBAAkB,EAAE,IAAA,EAAM,OAAO,MAAUD,IAAAA,YAAAA,IAAgB,CAAA,CAAA;AAAA,GACnE,GAAA,CAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAA,iBAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,OAAO,cAAe,CAAA,cAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA,CAAA;AAAA,KAC1D;AACA,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAO,cAAe,EAAA,CAAA;AAAA,KAC1B;AACA,IAAO,OAAA,kBAAA,CAAmB,OAAO,SAAS,CAAA,CAAA;AAAA,GAC3C,GAAA,CAAA;AAEH,EAAO,OAAA,eAAA;AAAA,IACH;AAAA,MACIC,gBAAAA,CAAiB,gBAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,MAAM,IAAI,CAAA;AAAA,MACjEA,gBAAiB,CAAA,eAAA,CAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAG,EAAA,CAAC,GAAG,KAAK,CAAA,KAAW,KAAK,CAAA;AAAA,KAC/E;AAAA,IACA,CAAC,OAAO,MAAW,KAAA;AACf,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,CAAC,OAAO,SAAW,EAAA;AAC7C,QAAO,OAAA,MAAA,CAAO,MAAS,GAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,OACvC;AACA,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,MAAA,CAAO,aAAa,IAAM,EAAA;AACpD,QAAA,MAAM,SACF,GAAA,MAAA,CAAO,SAAc,KAAA,QAAA,GAAW,IAAI,UAAA,CAAW,SAAU,CAAA,SAAS,CAAE,CAAA,IAAA,CAAK,CAAC,CAAA,GAAI,MAAO,CAAA,SAAA,CAAA;AACzF,QAAA,OAAOC,aAAc,CAAA,KAAA,EAAO,SAAW,EAAA,MAAM,IAAI,CAAI,GAAA,CAAA,CAAA;AAAA,OACzD;AACA,MAAA,OAAO,OAAO,MAAO,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAE,CAAC,CAAC,CAAA,CAAA;AAAA,KAC/C;AAAA,GACJ,CAAA;AACJ,CAAA;AAwBO,SAAS,gBACZ,CAAA,IAAA,EACA,MAA2C,GAAA,EACZ,EAAA;AAE/B,EAAOR,OAAAA,YAAAA;AAAA,IACH,kBAAA,CAA0B,MAAM,MAAoB,CAAA;AAAA,IACpD,kBAAA,CAAwB,MAAM,MAAoB,CAAA;AAAA,GACtD,CAAA;AACJ,CAAA;AChLO,SAAS,aACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACrB,EAAA;AACnB,EAAOK,OAAAA,gBAAAA,CAAiB,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAA,EAAG,CAAC,GAA6B,KAAA,CAAC,GAAG,GAAG,CAAC,CAAA,CAAA;AAC3G,CAAA;AAoBO,SAAS,aAAmB,CAAA,IAAA,EAAoB,MAAwC,GAAA,EAAuB,EAAA;AAClH,EAAOE,OAAAA,gBAAAA,CAAiB,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAA,EAAG,CAAC,OAA6B,KAAA,IAAI,GAAI,CAAA,OAAO,CAAC,CAAA,CAAA;AACnH,CAAA;AAoBO,SAAS,WACZ,CAAA,IAAA,EACA,MAAsC,GAAA,EACX,EAAA;AAC3B,EAAOP,OAAAA,YAAAA,CAAa,cAAc,IAAM,EAAA,MAAgB,GAAG,aAAc,CAAA,IAAA,EAAM,MAAgB,CAAC,CAAA,CAAA;AACpG,CAAA;ACvDO,SAAS,iBACZ,MAC0C,EAAA;AAE1C,EAAM,MAAA,WAAA,GAAc,OAAO,GAAI,CAAA,CAAC,GAAG,KAAK,MAAM,KAAK,CAAA,CAAA;AACnD,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,WAAY,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA,CAAA;AAC7D,EAAA,MAAM,UAAU,aAAc,CAAA,WAAA,CAAY,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAE9D,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,GAAI,cAAc,IACZ,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KACf,KAAA,MAAA,CACK,IAAI,CAAC,CAAC,GAAK,EAAA,KAAK,CAAMI,KAAAA,cAAAA,CAAe,MAAM,GAAkB,CAAA,EAAG,KAAK,CAAC,CACtE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,GAAA,KAAQ,GAAM,GAAA,GAAA,EAAK,CAAC,CAAA;AAAA,MAC1C,OAAA;AAAA,KACJ,GACA,EAAE,SAAU,EAAA;AAAA,IAClB,KAAO,EAAA,CAAC,MAAe,EAAA,KAAA,EAAO,MAAW,KAAA;AACrC,MAAA,MAAA,CAAO,OAAQ,CAAA,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC7B,QAAA,MAAA,GAAS,MAAM,KAAM,CAAA,MAAA,CAAO,GAAkB,CAAA,EAAG,OAAO,MAAM,CAAA,CAAA;AAAA,OACjE,CAAA,CAAA;AACD,MAAO,OAAA,MAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAaO,SAAS,iBACZ,MAC0C,EAAA;AAE1C,EAAM,MAAA,WAAA,GAAc,OAAO,GAAI,CAAA,CAAC,GAAG,KAAK,MAAM,KAAK,CAAA,CAAA;AACnD,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,WAAY,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA,CAAA;AAC7D,EAAA,MAAM,UAAU,aAAc,CAAA,WAAA,CAAY,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,KAAA,CAAA,CAAA;AAE9D,EAAA,OAAOH,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,OAAQ,EAAA,GAAI,EAAE,SAAU,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,SAAS,EAAC,CAAA;AAChB,MAAA,MAAA,CAAO,OAAQ,CAAA,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC7B,QAAA,MAAM,CAAC,KAAO,EAAA,SAAS,IAAI,KAAM,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AACnD,QAAS,MAAA,GAAA,SAAA,CAAA;AACT,QAAA,MAAA,CAAO,GAAgB,CAAI,GAAA,KAAA,CAAA;AAAA,OAC9B,CAAA,CAAA;AACD,MAAO,OAAA,CAAC,QAAQ,MAAM,CAAA,CAAA;AAAA,KAC1B;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAmBO,SAAS,eACZ,MAC+G,EAAA;AAC/G,EAAOC,OAAAA,YAAAA;AAAA,IACH,iBAAiB,MAAM,CAAA;AAAA,IACvB,iBAAiB,MAAM,CAAA;AAAA,GAC3B,CAAA;AACJ", "file": "index.node.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, SolanaError } from '@solana/errors';\n\n/** Checks the number of items in an array-like structure is expected. */\nexport function assertValidNumberOfItemsForCodec(\n    codecDescription: string,\n    expected: bigint | number,\n    actual: bigint | number,\n) {\n    if (expected !== actual) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, {\n            actual,\n            codecDescription,\n            expected,\n        });\n    }\n}\n", "import { isFixedSize } from '@solana/codecs-core';\n\n/**\n * Functionally, this type helper is equivalent to the identity type — i.e. `type Identity<T> = T`.\n * However, wrapping generic object mappings in this type significantly reduces the number\n * of instantiation expressions processed, which increases TypeScript performance and\n * prevents \"Type instantiation is excessively deep and possibly infinite\" errors.\n *\n * This works because TypeScript doesn't create a new level of nesting when encountering conditional generic types.\n * @see https://github.com/microsoft/TypeScript/issues/34933\n * @see https://github.com/kysely-org/kysely/pull/483\n */\nexport type DrainOuterGeneric<T> = [T] extends [unknown] ? T : never;\n\nexport function maxCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce(\n        (all, size) => (all === null || size === null ? null : Math.max(all, size)),\n        0 as number | null,\n    );\n}\n\nexport function sumCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce((all, size) => (all === null || size === null ? null : all + size), 0 as number | null);\n}\n\nexport function getFixedSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : null;\n}\n\nexport function getMaxSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : (codec.maxSize ?? null);\n}\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { getU32Decoder, getU32Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { getFixedSize, getMaxSize } from './utils';\n\n/**\n * Represents all the size options for array-like codecs\n * — i.e. `array`, `map` and `set`.\n *\n * It can be one of the following:\n * - a {@link NumberCodec} that prefixes its content with its size.\n * - a fixed number of items.\n * - or `'remainder'` to infer the number of items by dividing\n *   the rest of the byte array by the fixed size of its item.\n *   Note that this option is only available for fixed-size items.\n */\nexport type ArrayLikeCodecSize<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> =\n    | TPrefix\n    | number\n    | 'remainder';\n\n/** Defines the configs for array codecs. */\nexport type ArrayCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The size of the array.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Encodes an array of items.\n *\n * @param item - The encoder to use for the array's items.\n * @param config - A set of config for the encoder.\n */\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<TFrom[], 0>;\nexport function getArrayEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: ArrayCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom[]> {\n    const size = config.size ?? getU32Encoder();\n    const fixedSize = computeArrayLikeCodecSize(size, getFixedSize(item));\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize !== null\n            ? { fixedSize }\n            : {\n                  getSizeFromValue: (array: TFrom[]) => {\n                      const prefixSize = typeof size === 'object' ? getEncodedSize(array.length, size) : 0;\n                      return prefixSize + [...array].reduce((all, value) => all + getEncodedSize(value, item), 0);\n                  },\n                  maxSize,\n              }),\n        write: (array: TFrom[], bytes, offset) => {\n            if (typeof size === 'number') {\n                assertValidNumberOfItemsForCodec('array', size, array.length);\n            }\n            if (typeof size === 'object') {\n                offset = size.write(array.length, bytes, offset);\n            }\n            array.forEach(value => {\n                offset = item.write(value, bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Decodes an array of items.\n *\n * @param item - The encoder to use for the array's items.\n * @param config - A set of config for the encoder.\n */\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<TTo[], 0>;\nexport function getArrayDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: ArrayCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(item: Decoder<TTo>, config: ArrayCodecConfig<NumberDecoder> = {}): Decoder<TTo[]> {\n    const size = config.size ?? getU32Decoder();\n    const itemSize = getFixedSize(item);\n    const fixedSize = computeArrayLikeCodecSize(size, itemSize);\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize !== null ? { fixedSize } : { maxSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const array: TTo[] = [];\n            if (typeof size === 'object' && bytes.slice(offset).length === 0) {\n                return [array, offset];\n            }\n\n            if (size === 'remainder') {\n                while (offset < bytes.length) {\n                    const [value, newOffset] = item.read(bytes, offset);\n                    offset = newOffset;\n                    array.push(value);\n                }\n                return [array, offset];\n            }\n\n            const [resolvedSize, newOffset] = typeof size === 'number' ? [size, offset] : size.read(bytes, offset);\n            offset = newOffset;\n            for (let i = 0; i < resolvedSize; i += 1) {\n                const [value, newOffset] = item.read(bytes, offset);\n                offset = newOffset;\n                array.push(value);\n            }\n            return [array, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for an array of items.\n *\n * @param item - The codec to use for the array's items.\n * @param config - A set of config for the codec.\n */\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<TFrom[], TTo[], 0>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: ArrayCodecConfig<NumberCodec>,\n): VariableSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> = {},\n): Codec<TFrom[], TTo[]> {\n    return combineCodec(getArrayEncoder(item, config as object), getArrayDecoder(item, config as object));\n}\n\nfunction computeArrayLikeCodecSize(size: number | object | 'remainder', itemSize: number | null): number | null {\n    if (typeof size !== 'number') return null;\n    if (size === 0) return 0;\n    return itemSize === null ? null : itemSize * size;\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from '@solana/codecs-core';\n\n/** Defines the config for bitArray codecs. */\nexport type BitArrayCodecConfig = {\n    /**\n     * Whether to read the bits in reverse order.\n     * @defaultValue `false`\n     */\n    backward?: boolean;\n};\n\n/**\n * Encodes an array of booleans into bits.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the encoder.\n */\nexport function getBitArrayEncoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeEncoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createEncoder({\n        fixedSize: size,\n        write(value: boolean[], bytes, offset) {\n            const bytesToAdd: number[] = [];\n\n            for (let i = 0; i < size; i += 1) {\n                let byte = 0;\n                for (let j = 0; j < 8; j += 1) {\n                    const feature = Number(value[i * 8 + j] ?? 0);\n                    byte |= feature << (backward ? j : 7 - j);\n                }\n                if (backward) {\n                    bytesToAdd.unshift(byte);\n                } else {\n                    bytesToAdd.push(byte);\n                }\n            }\n\n            bytes.set(bytesToAdd, offset);\n            return size;\n        },\n    });\n}\n\n/**\n * Decodes bits into an array of booleans.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the decoder.\n */\nexport function getBitArrayDecoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeDecoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createDecoder({\n        fixedSize: size,\n        read(bytes, offset) {\n            assertByteArrayHasEnoughBytesForCodec('bitArray', size, bytes, offset);\n            const booleans: boolean[] = [];\n            let slice = bytes.slice(offset, offset + size);\n            slice = backward ? slice.reverse() : slice;\n\n            slice.forEach(byte => {\n                for (let i = 0; i < 8; i += 1) {\n                    if (backward) {\n                        booleans.push(Boolean(byte & 1));\n                        byte >>= 1;\n                    } else {\n                        booleans.push(Boolean(byte & 0b1000_0000));\n                        byte <<= 1;\n                    }\n                }\n            });\n\n            return [booleans, offset + size];\n        },\n    });\n}\n\n/**\n * An array of boolean codec that converts booleans to bits and vice versa.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the codec.\n */\nexport function getBitArrayCodec<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeCodec<boolean[], boolean[], TSize> {\n    return combineCodec(getBitArrayEncoder(size, config), getBitArrayDecoder(size, config));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\n/** Defines the config for boolean codecs. */\nexport type BooleanCodecConfig<TSize extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The number codec to delegate to.\n     * @defaultValue u8 size.\n     */\n    size?: TSize;\n};\n\n/**\n * Encodes booleans.\n *\n * @param config - A set of config for the encoder.\n */\nexport function getBooleanEncoder(): FixedSizeEncoder<boolean, 1>;\nexport function getBooleanEncoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<boolean, TSize>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder>): VariableSizeEncoder<boolean>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder> = {}): Encoder<boolean> {\n    return transformEncoder(config.size ?? getU8Encoder(), (value: boolean) => (value ? 1 : 0));\n}\n\n/**\n * Decodes booleans.\n *\n * @param config - A set of config for the decoder.\n */\nexport function getBooleanDecoder(): FixedSizeDecoder<boolean, 1>;\nexport function getBooleanDecoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<boolean, TSize>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder>): VariableSizeDecoder<boolean>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder> = {}): Decoder<boolean> {\n    return transformDecoder(config.size ?? getU8Decoder(), (value: bigint | number): boolean => Number(value) === 1);\n}\n\n/**\n * Creates a boolean codec.\n *\n * @param config - A set of config for the codec.\n */\nexport function getBooleanCodec(): FixedSizeCodec<boolean, boolean, 1>;\nexport function getBooleanCodec<TSize extends number>(\n    config: BooleanCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<boolean, boolean, TSize>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec>): VariableSizeCodec<boolean>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec> = {}): Codec<boolean> {\n    return combineCodec(getBooleanEncoder(config), getBooleanDecoder(config));\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\n/**\n * Encodes byte arrays as provided.\n *\n * To control the size of the encoded byte array, you can use\n * the `fixEncoderSize` or `addEncoderSizePrefix` functions.\n */\nexport function getBytesEncoder(): VariableSizeEncoder<ReadonlyUint8Array | Uint8Array> {\n    return createEncoder({\n        getSizeFromValue: value => value.length,\n        write: (value, bytes, offset) => {\n            bytes.set(value, offset);\n            return offset + value.length;\n        },\n    });\n}\n\n/**\n * Decodes byte arrays as-is.\n *\n * To control the size of the decoded byte array, you can use\n * the `fixDecoderSize` or `addDecoderSizePrefix` functions.\n */\nexport function getBytesDecoder(): VariableSizeDecoder<ReadonlyUint8Array> {\n    return createDecoder({\n        read: (bytes, offset) => {\n            const slice = bytes.slice(offset);\n            return [slice, offset + slice.length];\n        },\n    });\n}\n\n/**\n * Creates a sized bytes codec.\n *\n * To control the size of the encoded and decoded byte arrays,\n * you can use the `fixCodecSize` or `addCodecSizePrefix` functions.\n */\nexport function getBytesCodec(): VariableSizeCodec<ReadonlyUint8Array | Uint8Array, ReadonlyUint8Array> {\n    return combineCodec(getBytesEncoder(), getBytesDecoder());\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nconst enum HexC {\n    ZERO = 48, // 0\n    NINE = 57, // 9\n    A_UP = 65, // A\n    F_UP = 70, // F\n    A_LO = 97, // a\n    F_LO = 102, // f\n}\n\nconst INVALID_STRING_ERROR_BASE_CONFIG = {\n    alphabet: '0123456789abcdef',\n    base: 16,\n} as const;\n\nfunction charCodeToBase16(char: number) {\n    if (char >= HexC.ZERO && char <= HexC.NINE) return char - HexC.ZERO;\n    if (char >= HexC.A_UP && char <= HexC.F_UP) return char - (HexC.A_UP - 10);\n    if (char >= HexC.A_LO && char <= HexC.F_LO) return char - (HexC.A_LO - 10);\n}\n\n/** Encodes strings in base16. */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const len = value.length;\n            const al = len / 2;\n            if (len === 1) {\n                const c = value.charCodeAt(0);\n                const n = charCodeToBase16(c);\n                if (n === undefined) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                bytes.set([n], offset);\n                return 1 + offset;\n            }\n            const hexBytes = new Uint8Array(al);\n            for (let i = 0, j = 0; i < al; i++) {\n                const c1 = value.charCodeAt(j++);\n                const c2 = value.charCodeAt(j++);\n\n                const n1 = charCodeToBase16(c1);\n                const n2 = charCodeToBase16(c2);\n                if (n1 === undefined || (n2 === undefined && !Number.isNaN(c2))) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                hexBytes[i] = !Number.isNaN(c2) ? (n1 << 4) | (n2 ?? 0) : n1;\n            }\n\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/** Decodes strings in base16. */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/** Encodes and decodes strings in base16. */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import {\n    combineCodec,\n    containsBytes,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\nimport { getBase16Decoder } from '@solana/codecs-strings';\nimport { SOLANA_ERROR__CODECS__INVALID_CONSTANT, SolanaError } from '@solana/errors';\n\n/**\n * Creates a void encoder that always sets the provided byte array when encoding.\n */\nexport function getConstantEncoder<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeEncoder<void, TConstant['length']> {\n    return createEncoder({\n        fixedSize: constant.length,\n        write: (_, bytes, offset) => {\n            bytes.set(constant, offset);\n            return offset + constant.length;\n        },\n    });\n}\n\n/**\n * Creates a void decoder that reads the next bytes and fails if they do not match the provided constant.\n */\nexport function getConstantDecoder<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeDecoder<void, TConstant['length']> {\n    return createDecoder({\n        fixedSize: constant.length,\n        read: (bytes, offset) => {\n            const base16 = getBase16Decoder();\n            if (!containsBytes(bytes, constant, offset)) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_CONSTANT, {\n                    constant,\n                    data: bytes,\n                    hexConstant: base16.decode(constant),\n                    hexData: base16.decode(bytes),\n                    offset,\n                });\n            }\n            return [undefined, offset + constant.length];\n        },\n    });\n}\n\n/**\n * Creates a void codec that always sets the provided byte array\n * when encoding and, when decoding, asserts that the next\n * bytes match the provided byte array.\n */\nexport function getConstantCodec<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeCodec<void, void, TConstant['length']> {\n    return combineCodec(getConstantEncoder(constant), getConstantDecoder(constant));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Code<PERSON>,\n    combineCodec,\n    createDecoder,\n    createEnco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { DrainOuterGeneric, getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\ntype GetEncoderTypeFromItems<TItems extends readonly Encoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TItems]: TItems[I] extends Encoder<infer TFrom> ? TFrom : never;\n}>;\n\ntype GetDecoderTypeFromItems<TItems extends readonly Decoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TItems]: TItems[I] extends Decoder<infer TTo> ? TTo : never;\n}>;\n\n/**\n * Creates a encoder for a tuple-like array.\n *\n * @param items - The encoders to use for each item in the tuple.\n */\nexport function getTupleEncoder<const TItems extends readonly FixedSizeEncoder<any>[]>(\n    items: TItems,\n): FixedSizeEncoder<GetEncoderTypeFromItems<TItems>>;\nexport function getTupleEncoder<const TItems extends readonly Encoder<any>[]>(\n    items: TItems,\n): VariableSizeEncoder<GetEncoderTypeFromItems<TItems>>;\nexport function getTupleEncoder<const TItems extends readonly Encoder<any>[]>(\n    items: TItems,\n): Encoder<GetEncoderTypeFromItems<TItems>> {\n    type TFrom = GetEncoderTypeFromItems<TItems>;\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      items.map((item, index) => getEncodedSize(value[index], item)).reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (value: TFrom, bytes, offset) => {\n            assertValidNumberOfItemsForCodec('tuple', items.length, value.length);\n            items.forEach((item, index) => {\n                offset = item.write(value[index], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Creates a decoder for a tuple-like array.\n *\n * @param items - The decoders to use for each item in the tuple.\n */\n\nexport function getTupleDecoder<const TItems extends readonly FixedSizeDecoder<any>[]>(\n    items: TItems,\n): FixedSizeDecoder<GetDecoderTypeFromItems<TItems>>;\nexport function getTupleDecoder<const TItems extends readonly Decoder<any>[]>(\n    items: TItems,\n): VariableSizeDecoder<GetDecoderTypeFromItems<TItems>>;\nexport function getTupleDecoder<const TItems extends readonly Decoder<any>[]>(\n    items: TItems,\n): Decoder<GetDecoderTypeFromItems<TItems>> {\n    type TTo = GetDecoderTypeFromItems<TItems>;\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const values = [] as Array<any> & TTo;\n            items.forEach(item => {\n                const [newValue, newOffset] = item.read(bytes, offset);\n                values.push(newValue);\n                offset = newOffset;\n            });\n            return [values, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for a tuple-like array.\n *\n * @param items - The codecs to use for each item in the tuple.\n */\nexport function getTupleCodec<const TItems extends readonly FixedSizeCodec<any>[]>(\n    items: TItems,\n): FixedSizeCodec<GetEncoderTypeFromItems<TItems>, GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>>;\nexport function getTupleCodec<const TItems extends readonly Codec<any>[]>(\n    items: TItems,\n): VariableSizeCodec<\n    GetEncoderTypeFromItems<TItems>,\n    GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>\n>;\nexport function getTupleCodec<const TItems extends readonly Codec<any>[]>(\n    items: TItems,\n): Codec<GetEncoderTypeFromItems<TItems>, GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>> {\n    return combineCodec(\n        getTupleEncoder(items),\n        getTupleDecoder(items) as Decoder<GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>>,\n    );\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    getEncodedSize,\n    isFixedSize,\n    Offset,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE, SolanaError } from '@solana/errors';\n\nimport { DrainOuterGeneric, getMaxSize, maxCodecSizes } from './utils';\n\ntype GetEncoderTypeFromVariants<TVariants extends readonly Encoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TVariants]: TVariants[I] extends Encoder<infer TFrom> ? TFrom : never;\n}>[number];\n\ntype GetDecoderTypeFromVariants<TVariants extends readonly Decoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TVariants]: TVariants[I] extends Decoder<infer TFrom> ? TFrom : never;\n}>[number];\n\n/**\n * Creates a union encoder from the provided array of encoder.\n *\n * @param variants - The variant encoders of the union.\n * @param getIndexFromValue - A function that returns the index of the variant from the provided value.\n */\nexport function getUnionEncoder<const TVariants extends readonly Encoder<any>[]>(\n    variants: TVariants,\n    getIndexFromValue: (value: GetEncoderTypeFromVariants<TVariants>) => number,\n): Encoder<GetEncoderTypeFromVariants<TVariants>> {\n    type TFrom = GetEncoderTypeFromVariants<TVariants>;\n    const fixedSize = getUnionFixedSize(variants);\n    const write: Encoder<TFrom>['write'] = (variant, bytes, offset) => {\n        const index = getIndexFromValue(variant);\n        assertValidVariantIndex(variants, index);\n        return variants[index].write(variant, bytes, offset);\n    };\n\n    if (fixedSize !== null) {\n        return createEncoder({ fixedSize, write });\n    }\n\n    const maxSize = getUnionMaxSize(variants);\n    return createEncoder({\n        ...(maxSize !== null ? { maxSize } : {}),\n        getSizeFromValue: variant => {\n            const index = getIndexFromValue(variant);\n            assertValidVariantIndex(variants, index);\n            return getEncodedSize(variant, variants[index]);\n        },\n        write,\n    });\n}\n\n/**\n * Creates a union decoder from the provided array of decoder.\n *\n * @param variants - The variant decoders of the union.\n * @param getIndexFromBytes - A function that returns the index of the variant from the byte array.\n */\nexport function getUnionDecoder<const TVariants extends readonly Decoder<any>[]>(\n    variants: TVariants,\n    getIndexFromBytes: (bytes: ReadonlyUint8Array, offset: Offset) => number,\n): Decoder<GetDecoderTypeFromVariants<TVariants>> {\n    type TTo = GetDecoderTypeFromVariants<TVariants>;\n    const fixedSize = getUnionFixedSize(variants);\n    const read: Decoder<TTo>['read'] = (bytes, offset) => {\n        const index = getIndexFromBytes(bytes, offset);\n        assertValidVariantIndex(variants, index);\n        return variants[index].read(bytes, offset);\n    };\n\n    if (fixedSize !== null) {\n        return createDecoder({ fixedSize, read });\n    }\n\n    const maxSize = getUnionMaxSize(variants);\n    return createDecoder({ ...(maxSize !== null ? { maxSize } : {}), read });\n}\n\n/**\n * Creates a union codec from the provided array of codec.\n *\n * @param variants - The variant codecs of the union.\n * @param getIndexFromValue - A function that returns the index of the variant from the provided value.\n * @param getIndexFromBytes - A function that returns the index of the variant from the byte array.\n */\nexport function getUnionCodec<const TVariants extends readonly Codec<any>[]>(\n    variants: TVariants,\n    getIndexFromValue: (value: GetEncoderTypeFromVariants<TVariants>) => number,\n    getIndexFromBytes: (bytes: ReadonlyUint8Array, offset: Offset) => number,\n): Codec<\n    GetEncoderTypeFromVariants<TVariants>,\n    GetDecoderTypeFromVariants<TVariants> & GetEncoderTypeFromVariants<TVariants>\n> {\n    return combineCodec(\n        getUnionEncoder(variants, getIndexFromValue),\n        getUnionDecoder(variants, getIndexFromBytes) as Decoder<\n            GetDecoderTypeFromVariants<TVariants> & GetEncoderTypeFromVariants<TVariants>\n        >,\n    );\n}\n\nfunction assertValidVariantIndex(variants: readonly unknown[], index: number) {\n    if (typeof variants[index] === 'undefined') {\n        throw new SolanaError(SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE, {\n            maxRange: variants.length - 1,\n            minRange: 0,\n            variant: index,\n        });\n    }\n}\n\nfunction getUnionFixedSize<const TVariants extends readonly (Decoder<any> | Encoder<any>)[]>(variants: TVariants) {\n    if (variants.length === 0) return 0;\n    if (!isFixedSize(variants[0])) return null;\n    const variantSize = variants[0].fixedSize;\n    const sameSizedVariants = variants.every(variant => isFixedSize(variant) && variant.fixedSize === variantSize);\n    return sameSizedVariants ? variantSize : null;\n}\n\nfunction getUnionMaxSize<const TVariants extends readonly (Decoder<any> | Encoder<any>)[]>(variants: TVariants) {\n    return maxCodecSizes(variants.map(variant => getMaxSize(variant)));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Codec, combineCodec, Decoder, Encoder, transformDecoder, transformEncoder } from '@solana/codecs-core';\nimport { getU8Decoder, getU8Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\nimport { SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT, SolanaError } from '@solana/errors';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\nimport { getUnionDecoder, getUnionEncoder } from './union';\nimport { DrainOuterGeneric } from './utils';\n\n/**\n * Defines a discriminated union using discriminated union types.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * ```\n */\nexport type DiscriminatedUnion<\n    TDiscriminatorProperty extends string = '__kind',\n    TDiscriminatorValue extends string = string,\n> = {\n    [P in TDiscriminatorProperty]: TDiscriminatorValue;\n};\n\n/**\n * Extracts a variant from a discriminated union.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * type ClickEvent = GetDiscriminatedUnionVariant<WebPageEvent, '__kind', 'click'>;\n * // -> { __kind: 'click', x: number, y: number }\n * ```\n */\nexport type GetDiscriminatedUnionVariant<\n    TUnion extends DiscriminatedUnion<TDiscriminatorProperty>,\n    TDiscriminatorProperty extends string,\n    TDiscriminatorValue extends TUnion[TDiscriminatorProperty],\n> = Extract<TUnion, DiscriminatedUnion<TDiscriminatorProperty, TDiscriminatorValue>>;\n\n/**\n * Extracts a variant from a discriminated union without its discriminator.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * type ClickEvent = GetDiscriminatedUnionVariantContent<WebPageEvent, '__kind', 'click'>;\n * // -> { x: number, y: number }\n * ```\n */\nexport type GetDiscriminatedUnionVariantContent<\n    TUnion extends DiscriminatedUnion<TDiscriminatorProperty>,\n    TDiscriminatorProperty extends string,\n    TDiscriminatorValue extends TUnion[TDiscriminatorProperty],\n> = Omit<GetDiscriminatedUnionVariant<TUnion, TDiscriminatorProperty, TDiscriminatorValue>, TDiscriminatorProperty>;\n\n/** Defines the config for discriminated union codecs. */\nexport type DiscriminatedUnionCodecConfig<\n    TDiscriminatorProperty extends string = '__kind',\n    TDiscriminatorSize = NumberCodec | NumberDecoder | NumberEncoder,\n> = {\n    /**\n     * The property name of the discriminator.\n     * @defaultValue `__kind`.\n     */\n    discriminator?: TDiscriminatorProperty;\n    /**\n     * The codec to use for the enum discriminator prefixing the variant.\n     * @defaultValue u8 prefix.\n     */\n    size?: TDiscriminatorSize;\n};\n\ntype DiscriminatorValue = bigint | boolean | number | string | null | undefined;\ntype Variants<T> = readonly (readonly [DiscriminatorValue, T])[];\ntype ArrayIndices<T extends readonly unknown[]> = Exclude<Partial<T>['length'], T['length']> & number;\n\ntype GetEncoderTypeFromVariants<\n    TVariants extends Variants<Encoder<any>>,\n    TDiscriminatorProperty extends string,\n> = DrainOuterGeneric<{\n    [I in ArrayIndices<TVariants>]: (TVariants[I][1] extends Encoder<infer TFrom>\n        ? TFrom extends object\n            ? TFrom\n            : object\n        : never) & { [P in TDiscriminatorProperty]: TVariants[I][0] };\n}>[ArrayIndices<TVariants>];\n\ntype GetDecoderTypeFromVariants<\n    TVariants extends Variants<Decoder<any>>,\n    TDiscriminatorProperty extends string,\n> = DrainOuterGeneric<{\n    [I in ArrayIndices<TVariants>]: (TVariants[I][1] extends Decoder<infer TTo>\n        ? TTo extends object\n            ? TTo\n            : object\n        : never) & { [P in TDiscriminatorProperty]: TVariants[I][0] };\n}>[ArrayIndices<TVariants>];\n\n/**\n * Creates a discriminated union encoder.\n *\n * @param variants - The variant encoders of the discriminated union.\n * @param config - A set of config for the encoder.\n */\nexport function getDiscriminatedUnionEncoder<\n    const TVariants extends Variants<Encoder<any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberEncoder> = {},\n): Encoder<GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>> {\n    type TFrom = GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>;\n    const discriminatorProperty = (config.discriminator ?? '__kind') as TDiscriminatorProperty;\n    const prefix = config.size ?? getU8Encoder();\n    return getUnionEncoder(\n        variants.map(([, variant], index) =>\n            transformEncoder(getTupleEncoder([prefix, variant]), (value: TFrom): [number, TFrom] => [index, value]),\n        ),\n        value => getVariantDiscriminator(variants, value[discriminatorProperty]),\n    );\n}\n\n/**\n * Creates a discriminated union decoder.\n *\n * @param variants - The variant decoders of the discriminated union.\n * @param config - A set of config for the decoder.\n */\nexport function getDiscriminatedUnionDecoder<\n    const TVariants extends Variants<Decoder<any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberDecoder> = {},\n): Decoder<GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty>> {\n    const discriminatorProperty = config.discriminator ?? '__kind';\n    const prefix = config.size ?? getU8Decoder();\n    return getUnionDecoder(\n        variants.map(([discriminator, variant]) =>\n            transformDecoder(getTupleDecoder([prefix, variant]), ([, value]) => ({\n                [discriminatorProperty]: discriminator,\n                ...value,\n            })),\n        ),\n        (bytes, offset) => Number(prefix.read(bytes, offset)[0]),\n    );\n}\n\n/**\n * Creates a discriminated union codec.\n *\n * @param variants - The variant codecs of the discriminated union.\n * @param config - A set of config for the codec.\n */\nexport function getDiscriminatedUnionCodec<\n    const TVariants extends Variants<Codec<any, any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberCodec> = {},\n): Codec<\n    GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>,\n    GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty> &\n        GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>\n> {\n    return combineCodec(\n        getDiscriminatedUnionEncoder(variants, config),\n        getDiscriminatedUnionDecoder(variants, config) as Decoder<\n            GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty> &\n                GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>\n        >,\n    );\n}\n\nfunction getVariantDiscriminator<const TVariants extends Variants<Decoder<any> | Encoder<any>>>(\n    variants: TVariants,\n    discriminatorValue: DiscriminatorValue,\n) {\n    const discriminator = variants.findIndex(([key]) => discriminatorValue === key);\n    if (discriminator < 0) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT, {\n            value: discriminatorValue,\n            variants: variants.map(([key]) => key),\n        });\n    }\n    return discriminator;\n}\n\n/** @deprecated Use `getDiscriminatedUnionEncoder` instead. */\nexport const getDataEnumEncoder = getDiscriminatedUnionEncoder;\n\n/** @deprecated Use `getDiscriminatedUnionDecoder` instead. */\nexport const getDataEnumDecoder = getDiscriminatedUnionDecoder;\n\n/** @deprecated Use `getDiscriminatedUnionCodec` instead. */\nexport const getDataEnumCodec = getDiscriminatedUnionCodec;\n", "/**\n * Defines the \"lookup object\" of an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * ```\n */\nexport type EnumLookupObject = { [key: string]: number | string };\n\n/**\n * Returns the allowed input for an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * type DirectionInput = GetEnumFrom<Direction>; // \"Left\" | \"Right\" | 0 | 1\n * ```\n */\nexport type GetEnumFrom<TEnum extends EnumLookupObject> = TEnum[keyof TEnum] | keyof TEnum;\n\n/**\n * Returns all the available variants of an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * type DirectionOutput = GetEnumTo<Direction>; // 0 | 1\n * ```\n */\nexport type GetEnumTo<TEnum extends EnumLookupObject> = TEnum[keyof TEnum];\n\nexport function getEnumStats(constructor: EnumLookupObject) {\n    const numericalValues = [\n        ...new Set(Object.values(constructor).filter(v => typeof v === 'number') as number[]),\n    ].sort();\n    const enumRecord = Object.fromEntries(Object.entries(constructor).slice(numericalValues.length)) as Record<\n        string,\n        number | string\n    >;\n    const enumKeys = Object.keys(enumRecord);\n    const enumValues = Object.values(enumRecord);\n    const stringValues: string[] = [\n        ...new Set([...enumKeys, ...enumValues.filter((v): v is string => typeof v === 'string')]),\n    ];\n\n    return { enumKeys, enumRecord, enumValues, numericalValues, stringValues };\n}\n\nexport function getEnumIndexFromVariant({\n    enumKeys,\n    enumValues,\n    variant,\n}: {\n    enumKeys: string[];\n    enumValues: (number | string)[];\n    variant: number | string | symbol;\n}): number {\n    const valueIndex = findLastIndex(enumValues, value => value === variant);\n    if (valueIndex >= 0) return valueIndex;\n    return enumKeys.findIndex(key => key === variant);\n}\n\nexport function getEnumIndexFromDiscriminator({\n    discriminator,\n    enumKeys,\n    enumValues,\n    useValuesAsDiscriminators,\n}: {\n    discriminator: number;\n    enumKeys: string[];\n    enumValues: (number | string)[];\n    useValuesAsDiscriminators: boolean;\n}): number {\n    if (!useValuesAsDiscriminators) {\n        return discriminator >= 0 && discriminator < enumKeys.length ? discriminator : -1;\n    }\n    return findLastIndex(enumValues, value => value === discriminator);\n}\n\nfunction findLastIndex<T>(array: Array<T>, predicate: (value: T, index: number, obj: T[]) => boolean): number {\n    let l = array.length;\n    while (l--) {\n        if (predicate(array[l], l, array)) return l;\n    }\n    return -1;\n}\n\nexport function formatNumericalValues(values: number[]): string {\n    if (values.length === 0) return '';\n    let range: [number, number] = [values[0], values[0]];\n    const ranges: string[] = [];\n    for (let index = 1; index < values.length; index++) {\n        const value = values[index];\n        if (range[1] + 1 === value) {\n            range[1] = value;\n        } else {\n            ranges.push(range[0] === range[1] ? `${range[0]}` : `${range[0]}-${range[1]}`);\n            range = [value, value];\n        }\n    }\n    ranges.push(range[0] === range[1] ? `${range[0]}` : `${range[0]}-${range[1]}`);\n    return ranges.join(', ');\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\nimport {\n    SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS,\n    SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT,\n    SolanaError,\n} from '@solana/errors';\n\nimport {\n    EnumLookupObject,\n    formatNumericalValues,\n    GetEnumFrom,\n    getEnumIndexFromDiscriminator,\n    getEnumIndexFromVariant,\n    getEnumStats,\n    GetEnumTo,\n} from './enum-helpers';\n\n/** Defines the config for enum codecs. */\nexport type EnumCodecConfig<TDiscriminator extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The codec to use for the enum discriminator.\n     * @defaultValue u8 discriminator.\n     */\n    size?: TDiscriminator;\n\n    /**\n     * When set to `true`, numeric values will be used as discriminantors and\n     * an error will be thrown if a string value is found on the enum.\n     * @defaultValue `false`\n     */\n    useValuesAsDiscriminators?: boolean;\n};\n\n/**\n * Creates an enum encoder.\n *\n * @param constructor - The constructor of the enum.\n * @param config - A set of config for the encoder.\n */\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberEncoder>, 'size'>,\n): FixedSizeEncoder<GetEnumFrom<TEnum>, 1>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<GetEnumFrom<TEnum>, TSize>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<GetEnumFrom<TEnum>>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberEncoder> = {},\n): Encoder<GetEnumFrom<TEnum>> {\n    const prefix = config.size ?? getU8Encoder();\n    const useValuesAsDiscriminators = config.useValuesAsDiscriminators ?? false;\n    const { enumKeys, enumValues, numericalValues, stringValues } = getEnumStats(constructor);\n    if (useValuesAsDiscriminators && enumValues.some(value => typeof value === 'string')) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS, {\n            stringValues: enumValues.filter((v): v is string => typeof v === 'string'),\n        });\n    }\n    return transformEncoder(prefix, (variant: GetEnumFrom<TEnum>): number => {\n        const index = getEnumIndexFromVariant({ enumKeys, enumValues, variant });\n        if (index < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT, {\n                formattedNumericalValues: formatNumericalValues(numericalValues),\n                numericalValues,\n                stringValues,\n                variant,\n            });\n        }\n        return useValuesAsDiscriminators ? (enumValues[index] as number) : index;\n    });\n}\n\n/**\n * Creates an enum decoder.\n *\n * @param constructor - The constructor of the enum.\n * @param config - A set of config for the decoder.\n */\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberDecoder>, 'size'>,\n): FixedSizeDecoder<GetEnumTo<TEnum>, 1>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<GetEnumTo<TEnum>, TSize>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<GetEnumTo<TEnum>>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberDecoder> = {},\n): Decoder<GetEnumTo<TEnum>> {\n    const prefix = config.size ?? getU8Decoder();\n    const useValuesAsDiscriminators = config.useValuesAsDiscriminators ?? false;\n    const { enumKeys, enumValues, numericalValues } = getEnumStats(constructor);\n    if (useValuesAsDiscriminators && enumValues.some(value => typeof value === 'string')) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS, {\n            stringValues: enumValues.filter((v): v is string => typeof v === 'string'),\n        });\n    }\n    return transformDecoder(prefix, (value: bigint | number): GetEnumTo<TEnum> => {\n        const discriminator = Number(value);\n        const index = getEnumIndexFromDiscriminator({\n            discriminator,\n            enumKeys,\n            enumValues,\n            useValuesAsDiscriminators,\n        });\n        if (index < 0) {\n            const validDiscriminators = useValuesAsDiscriminators\n                ? numericalValues\n                : [...Array(enumKeys.length).keys()];\n            throw new SolanaError(SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE, {\n                discriminator,\n                formattedValidDiscriminators: formatNumericalValues(validDiscriminators),\n                validDiscriminators,\n            });\n        }\n        return enumValues[index] as GetEnumTo<TEnum>;\n    });\n}\n\n/**\n * Creates an enum codec.\n *\n * @param constructor - The constructor of the enum.\n * @param config - A set of config for the codec.\n */\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberCodec>, 'size'>,\n): FixedSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>, 1>;\nexport function getEnumCodec<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>, TSize>;\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberCodec>,\n): VariableSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>>;\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberCodec> = {},\n): Codec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>> {\n    return combineCodec(getEnumEncoder(constructor, config), getEnumDecoder(constructor, config));\n}\n\n/** @deprecated Use `getEnumEncoder` instead. */\nexport const getScalarEnumEncoder = getEnumEncoder;\n\n/** @deprecated Use `getEnumDecoder` instead. */\nexport const getScalarEnumDecoder = getEnumDecoder;\n\n/** @deprecated Use `getEnumCodec` instead. */\nexport const getScalarEnumCodec = getEnumCodec;\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/**\n * Prefixes a given encoder with a list of void encoders.\n * All void encoders are hidden from the returned encoder.\n */\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    prefixedEncoders: readonly FixedSizeEncoder<void>[],\n): FixedSizeEncoder<TFrom>;\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    prefixedEncoders: readonly Encoder<void>[],\n): VariableSizeEncoder<TFrom>;\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    prefixedEncoders: readonly Encoder<void>[],\n): Encoder<TFrom> {\n    return transformEncoder(\n        getTupleEncoder([...prefixedEncoders, encoder]) as Encoder<readonly [...void[], TFrom]>,\n        (value: TFrom) => [...prefixedEncoders.map(() => undefined), value] as const,\n    );\n}\n\n/**\n * Prefixes a given decoder with a list of void decoder.\n * All void decoder are hidden from the returned decoder.\n */\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    prefixedDecoders: readonly FixedSizeDecoder<void>[],\n): FixedSizeDecoder<TTo>;\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    prefixedDecoders: readonly Decoder<void>[],\n): VariableSizeDecoder<TTo>;\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    prefixedDecoders: readonly Decoder<void>[],\n): Decoder<TTo> {\n    return transformDecoder(\n        getTupleDecoder([...prefixedDecoders, decoder]) as Decoder<readonly [...void[], TTo]>,\n        tuple => tuple[tuple.length - 1] as TTo,\n    );\n}\n\n/**\n * Prefixes a given codec with a list of void codec.\n * All void codec are hidden from the returned codec.\n */\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    prefixedCodecs: readonly FixedSizeCodec<void>[],\n): FixedSizeCodec<TFrom, TTo>;\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefixedCodecs: readonly Codec<void>[],\n): VariableSizeCodec<TFrom, TTo>;\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefixedCodecs: readonly Codec<void>[],\n): Codec<TFrom, TTo> {\n    return combineCodec(getHiddenPrefixEncoder(codec, prefixedCodecs), getHiddenPrefixDecoder(codec, prefixedCodecs));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/**\n * Suffixes a given encoder with a list of void encoders.\n * All void encoders are hidden from the returned encoder.\n */\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    suffixedEncoders: readonly FixedSizeEncoder<void>[],\n): FixedSizeEncoder<TFrom>;\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    suffixedEncoders: readonly Encoder<void>[],\n): VariableSizeEncoder<TFrom>;\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    suffixedEncoders: readonly Encoder<void>[],\n): Encoder<TFrom> {\n    return transformEncoder(\n        getTupleEncoder([encoder, ...suffixedEncoders]) as Encoder<readonly [TFrom, ...void[]]>,\n        (value: TFrom) => [value, ...suffixedEncoders.map(() => undefined)] as const,\n    );\n}\n\n/**\n * Suffixes a given decoder with a list of void decoder.\n * All void decoder are hidden from the returned decoder.\n */\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    suffixedDecoders: readonly FixedSizeDecoder<void>[],\n): FixedSizeDecoder<TTo>;\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    suffixedDecoders: readonly Decoder<void>[],\n): VariableSizeDecoder<TTo>;\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    suffixedDecoders: readonly Decoder<void>[],\n): Decoder<TTo> {\n    return transformDecoder(\n        getTupleDecoder([decoder, ...suffixedDecoders]) as Decoder<readonly [TTo, ...void[]]>,\n        tuple => tuple[0],\n    );\n}\n\n/**\n * Suffixes a given codec with a list of void codec.\n * All void codec are hidden from the returned codec.\n */\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    suffixedCodecs: readonly FixedSizeCodec<void>[],\n): FixedSizeCodec<TFrom, TTo>;\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    suffixedCodecs: readonly Codec<void>[],\n): VariableSizeCodec<TFrom, TTo>;\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    suffixedCodecs: readonly Codec<void>[],\n): Codec<TFrom, TTo> {\n    return combineCodec(getHiddenSuffixEncoder(codec, suffixedCodecs), getHiddenSuffixDecoder(codec, suffixedCodecs));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/** Defines the config for Map codecs. */\nexport type MapCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The size of the array.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Creates a encoder for a map.\n *\n * @param key - The encoder to use for the map's keys.\n * @param value - The encoder to use for the map's values.\n * @param config - A set of config for the encoder.\n */\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>, 0>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: FixedSizeEncoder<TFromKey>,\n    value: FixedSizeEncoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config?: MapCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> = {},\n): Encoder<Map<TFromKey, TFromValue>> {\n    return transformEncoder(\n        getArrayEncoder(getTupleEncoder([key, value]), config as object),\n        (map: Map<TFromKey, TFromValue>): [TFromKey, TFromValue][] => [...map.entries()],\n    );\n}\n\n/**\n * Creates a decoder for a map.\n *\n * @param key - The decoder to use for the map's keys.\n * @param value - The decoder to use for the map's values.\n * @param config - A set of config for the decoder.\n */\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Map<TToKey, TToValue>, 0>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: FixedSizeDecoder<TToKey>,\n    value: FixedSizeDecoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config?: MapCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> = {},\n): Decoder<Map<TToKey, TToValue>> {\n    return transformDecoder(\n        getArrayDecoder(getTupleDecoder([key, value]), config as object) as Decoder<[TToKey, TToValue][]>,\n        (entries: [TToKey, TToValue][]): Map<TToKey, TToValue> => new Map(entries),\n    );\n}\n\n/**\n * Creates a codec for a map.\n *\n * @param key - The codec to use for the map's keys.\n * @param value - The codec to use for the map's values.\n * @param config - A set of config for the codec.\n */\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>, 0>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: FixedSizeCodec<TFromKey, TToKey>,\n    value: FixedSizeCodec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config?: MapCodecConfig<NumberCodec>,\n): VariableSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> = {},\n): Codec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>> {\n    return combineCodec(getMapEncoder(key, value, config as object), getMapDecoder(key, value, config as object));\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\n\n/**\n * Creates a void encoder.\n */\nexport function getUnitEncoder(): FixedSizeEncoder<void, 0> {\n    return createEncoder({\n        fixedSize: 0,\n        write: (_value, _bytes, offset) => offset,\n    });\n}\n\n/**\n * Creates a void decoder.\n */\nexport function getUnitDecoder(): FixedSizeDecoder<void, 0> {\n    return createDecoder({\n        fixedSize: 0,\n        read: (_bytes: ReadonlyUint8Array | Uint8Array, offset) => [undefined, offset],\n    });\n}\n\n/**\n * Creates a void codec.\n */\nexport function getUnitCodec(): FixedSizeCodec<void, void, 0> {\n    return combineCodec(getUnitEncoder(), getUnitDecoder());\n}\n", "import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    contains<PERSON>ytes,\n    Decoder,\n    Encoder,\n    fixDecoderSize,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoderSize,\n    ReadonlyUint8Array,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\nimport { getBooleanDecoder, getBooleanEncoder } from './boolean';\nimport { getConstantDecoder, getConstantEncoder } from './constant';\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\nimport { getUnionDecoder, getUnionEncoder } from './union';\nimport { getUnitDecoder, getUnitEncoder } from './unit';\n\n/** Defines the config for nullable codecs. */\nexport type NullableCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * Defines how the `None` (or `null`) value should be represented.\n     *\n     * By default, no none value is used. This means a `null` value will be\n     * represented by the absence of the item.\n     *\n     * When `'zeroes'` is provided, a `null` value will skip the bytes that would\n     * have been used for the item. Note that this returns a fixed-size codec\n     * and thus will only work if the item codec is of fixed size.\n     *\n     * When a custom byte array is provided, a `null` value will be represented\n     * by the provided byte array. Note that this returns a variable-size codec\n     * since the byte array representing `null` does not need to match the size\n     * of the item codec.\n     *\n     * @defaultValue No none value is used.\n     */\n    noneValue?: ReadonlyUint8Array | 'zeroes';\n\n    /**\n     * The codec to use for the boolean prefix, if any.\n     *\n     * By default a `u8` number is used as a prefix to determine if the value is `null`.\n     * The value `0` is encoded for `null` and `1` if the value is present.\n     * This can be set to any number codec to customize the prefix.\n     *\n     * When `null` is provided, no prefix is used and the `noneValue` is used to\n     * determine if the value is `null`. If no `noneValue` is provided, then the\n     * absence of any bytes is used to determine if the value is `null`.\n     *\n     * @defaultValue `u8` prefix.\n     */\n    prefix?: TPrefix | null;\n};\n\n/**\n * Creates a encoder for an optional value using `null` as the `None` value.\n *\n * @param item - The encoder to use for the value that may be present.\n * @param config - A set of config for the encoder.\n */\nexport function getNullableEncoder<TFrom, TSize extends number>(\n    item: FixedSizeEncoder<TFrom, TSize>,\n    config: NullableCodecConfig<NumberEncoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeEncoder<TFrom | null, TSize>;\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: NullableCodecConfig<FixedSizeNumberEncoder> & { noneValue: 'zeroes' },\n): FixedSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: NullableCodecConfig<NumberEncoder> & { noneValue: 'zeroes' },\n): VariableSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: NullableCodecConfig<NumberEncoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: NullableCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom | null> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformEncoder(getUnitEncoder(), (_boolean: boolean) => undefined);\n        }\n        return getBooleanEncoder({ size: config.prefix ?? getU8Encoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixEncoderSize(getUnitEncoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitEncoder();\n        }\n        return getConstantEncoder(config.noneValue);\n    })();\n\n    return getUnionEncoder(\n        [\n            transformEncoder(getTupleEncoder([prefix, noneValue]), (_value: null): [boolean, void] => [\n                false,\n                undefined,\n            ]),\n            transformEncoder(getTupleEncoder([prefix, item]), (value: TFrom): [boolean, TFrom] => [true, value]),\n        ],\n        variant => Number(variant !== null),\n    );\n}\n\n/**\n * Creates a decoder for an optional value using `null` as the `None` value.\n *\n * @param item - The decoder to use for the value that may be present.\n * @param config - A set of config for the decoder.\n */\nexport function getNullableDecoder<TTo, TSize extends number>(\n    item: FixedSizeDecoder<TTo, TSize>,\n    config: NullableCodecConfig<NumberDecoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeDecoder<TTo | null, TSize>;\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: NullableCodecConfig<FixedSizeNumberDecoder> & { noneValue: 'zeroes' },\n): FixedSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: NullableCodecConfig<NumberDecoder> & { noneValue: 'zeroes' },\n): VariableSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: NullableCodecConfig<NumberDecoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: NullableCodecConfig<NumberDecoder> = {},\n): Decoder<TTo | null> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformDecoder(getUnitDecoder(), () => false);\n        }\n        return getBooleanDecoder({ size: config.prefix ?? getU8Decoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixDecoderSize(getUnitDecoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitDecoder();\n        }\n        return getConstantDecoder(config.noneValue);\n    })();\n\n    return getUnionDecoder(\n        [\n            transformDecoder(getTupleDecoder([prefix, noneValue]), () => null),\n            transformDecoder(getTupleDecoder([prefix, item]), ([, value]): TTo => value),\n        ],\n        (bytes, offset) => {\n            if (config.prefix === null && !config.noneValue) {\n                return Number(offset < bytes.length);\n            }\n            if (config.prefix === null && config.noneValue != null) {\n                const zeroValue =\n                    config.noneValue === 'zeroes' ? new Uint8Array(noneValue.fixedSize).fill(0) : config.noneValue;\n                return containsBytes(bytes, zeroValue, offset) ? 0 : 1;\n            }\n            return Number(prefix.read(bytes, offset)[0]);\n        },\n    );\n}\n\n/**\n * Creates a codec for an optional value using `null` as the `None` value.\n *\n * @param item - The codec to use for the value that may be present.\n * @param config - A set of config for the codec.\n */\nexport function getNullableCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    item: FixedSizeCodec<TFrom, TTo, TSize>,\n    config: NullableCodecConfig<NumberCodec> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeCodec<TFrom | null, TTo | null, TSize>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: NullableCodecConfig<FixedSizeNumberCodec> & { noneValue: 'zeroes' },\n): FixedSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: NullableCodecConfig<NumberCodec> & { noneValue: 'zeroes' },\n): VariableSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: NullableCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: NullableCodecConfig<NumberCodec> = {},\n): Codec<TFrom | null, TTo | null> {\n    type ConfigCast = NullableCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array };\n    return combineCodec(\n        getNullableEncoder<TFrom>(item, config as ConfigCast),\n        getNullableDecoder<TTo>(item, config as ConfigCast),\n    );\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\n\n/** Defines the config for set codecs. */\nexport type SetCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The size of the set.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Encodes an set of items.\n *\n * @param item - The encoder to use for the set's items.\n * @param config - A set of config for the encoder.\n */\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Set<TFrom>, 0>;\nexport function getSetEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: SetCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> = {},\n): Encoder<Set<TFrom>> {\n    return transformEncoder(getArrayEncoder(item, config as object), (set: Set<TFrom>): TFrom[] => [...set]);\n}\n\n/**\n * Decodes an set of items.\n *\n * @param item - The encoder to use for the set's items.\n * @param config - A set of config for the encoder.\n */\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Set<TTo>, 0>;\nexport function getSetDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: SetCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(item: Decoder<TTo>, config: SetCodecConfig<NumberDecoder> = {}): Decoder<Set<TTo>> {\n    return transformDecoder(getArrayDecoder(item, config as object), (entries: TTo[]): Set<TTo> => new Set(entries));\n}\n\n/**\n * Creates a codec for an set of items.\n *\n * @param item - The codec to use for the set's items.\n * @param config - A set of config for the codec.\n */\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>, 0>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: SetCodecConfig<NumberCodec>,\n): VariableSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> = {},\n): Codec<Set<TFrom>, Set<TTo>> {\n    return combineCodec(getSetEncoder(item, config as object), getSetDecoder(item, config as object));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Code<PERSON>,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { DrainOuterGeneric, getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\ntype Fields<T> = readonly (readonly [string, T])[];\ntype ArrayIndices<T extends readonly unknown[]> = Exclude<Partial<T>['length'], T['length']> & number;\n\ntype GetEncoderTypeFromFields<TFields extends Fields<Encoder<any>>> = DrainOuterGeneric<{\n    [I in ArrayIndices<TFields> as TFields[I][0]]: TFields[I][1] extends Encoder<infer TFrom> ? TFrom : never;\n}>;\n\ntype GetDecoderTypeFromFields<TFields extends Fields<Decoder<any>>> = DrainOuterGeneric<{\n    [I in ArrayIndices<TFields> as TFields[I][0]]: TFields[I][1] extends Decoder<infer TTo> ? TTo : never;\n}>;\n\n/**\n * Creates a encoder for a custom object.\n *\n * @param fields - The name and encoder of each field.\n */\nexport function getStructEncoder<const TFields extends Fields<FixedSizeEncoder<any>>>(\n    fields: TFields,\n): FixedSizeEncoder<GetEncoderTypeFromFields<TFields>>;\nexport function getStructEncoder<const TFields extends Fields<Encoder<any>>>(\n    fields: TFields,\n): VariableSizeEncoder<GetEncoderTypeFromFields<TFields>>;\nexport function getStructEncoder<const TFields extends Fields<Encoder<any>>>(\n    fields: TFields,\n): Encoder<GetEncoderTypeFromFields<TFields>> {\n    type TFrom = GetEncoderTypeFromFields<TFields>;\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      fields\n                          .map(([key, codec]) => getEncodedSize(value[key as keyof TFrom], codec))\n                          .reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (struct: TFrom, bytes, offset) => {\n            fields.forEach(([key, codec]) => {\n                offset = codec.write(struct[key as keyof TFrom], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Creates a decoder for a custom object.\n *\n * @param fields - The name and decoder of each field.\n */\nexport function getStructDecoder<const TFields extends Fields<FixedSizeDecoder<any>>>(\n    fields: TFields,\n): FixedSizeDecoder<GetDecoderTypeFromFields<TFields>>;\nexport function getStructDecoder<const TFields extends Fields<Decoder<any>>>(\n    fields: TFields,\n): VariableSizeDecoder<GetDecoderTypeFromFields<TFields>>;\nexport function getStructDecoder<const TFields extends Fields<Decoder<any>>>(\n    fields: TFields,\n): Decoder<GetDecoderTypeFromFields<TFields>> {\n    type TTo = GetDecoderTypeFromFields<TFields>;\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const struct = {} as TTo;\n            fields.forEach(([key, codec]) => {\n                const [value, newOffset] = codec.read(bytes, offset);\n                offset = newOffset;\n                struct[key as keyof TTo] = value;\n            });\n            return [struct, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for a custom object.\n *\n * @param fields - The name and codec of each field.\n */\nexport function getStructCodec<const TFields extends Fields<FixedSizeCodec<any>>>(\n    fields: TFields,\n): FixedSizeCodec<\n    GetEncoderTypeFromFields<TFields>,\n    GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>\n>;\nexport function getStructCodec<const TFields extends Fields<Codec<any>>>(\n    fields: TFields,\n): VariableSizeCodec<\n    GetEncoderTypeFromFields<TFields>,\n    GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>\n>;\nexport function getStructCodec<const TFields extends Fields<Codec<any>>>(\n    fields: TFields,\n): Codec<GetEncoderTypeFromFields<TFields>, GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>> {\n    return combineCodec(\n        getStructEncoder(fields),\n        getStructDecoder(fields) as Decoder<GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>>,\n    );\n}\n"]}