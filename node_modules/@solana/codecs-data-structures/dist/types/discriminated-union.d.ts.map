{"version": 3, "file": "discriminated-union.d.ts", "sourceRoot": "", "sources": ["../../src/discriminated-union.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAgB,OAAO,EAAE,OAAO,EAAsC,MAAM,qBAAqB,CAAC;AAChH,OAAO,EAA8B,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAK/G,OAAO,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AAE5C;;;;;;;;;GASG;AACH,MAAM,MAAM,kBAAkB,CAC1B,sBAAsB,SAAS,MAAM,GAAG,QAAQ,EAChD,mBAAmB,SAAS,MAAM,GAAG,MAAM,IAC3C;KACC,CAAC,IAAI,sBAAsB,GAAG,mBAAmB;CACrD,CAAC;AAEF;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,4BAA4B,CACpC,MAAM,SAAS,kBAAkB,CAAC,sBAAsB,CAAC,EACzD,sBAAsB,SAAS,MAAM,EACrC,mBAAmB,SAAS,MAAM,CAAC,sBAAsB,CAAC,IAC1D,OAAO,CAAC,MAAM,EAAE,kBAAkB,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC,CAAC;AAErF;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,mCAAmC,CAC3C,MAAM,SAAS,kBAAkB,CAAC,sBAAsB,CAAC,EACzD,sBAAsB,SAAS,MAAM,EACrC,mBAAmB,SAAS,MAAM,CAAC,sBAAsB,CAAC,IAC1D,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,sBAAsB,EAAE,mBAAmB,CAAC,EAAE,sBAAsB,CAAC,CAAC;AAEpH,yDAAyD;AACzD,MAAM,MAAM,6BAA6B,CACrC,sBAAsB,SAAS,MAAM,GAAG,QAAQ,EAChD,kBAAkB,GAAG,WAAW,GAAG,aAAa,GAAG,aAAa,IAChE;IACA;;;OAGG;IACH,aAAa,CAAC,EAAE,sBAAsB,CAAC;IACvC;;;OAGG;IACH,IAAI,CAAC,EAAE,kBAAkB,CAAC;CAC7B,CAAC;AAEF,KAAK,kBAAkB,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;AAChF,KAAK,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACjE,KAAK,YAAY,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;AAEtG,KAAK,0BAA0B,CAC3B,SAAS,SAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EACxC,sBAAsB,SAAS,MAAM,IACrC,iBAAiB,CAAC;KACjB,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,KAAK,CAAC,GACvE,KAAK,SAAS,MAAM,GAChB,KAAK,GACL,MAAM,GACV,KAAK,CAAC,GAAG;SAAG,CAAC,IAAI,sBAAsB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAAE;CACpE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AAE5B,KAAK,0BAA0B,CAC3B,SAAS,SAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EACxC,sBAAsB,SAAS,MAAM,IACrC,iBAAiB,CAAC;KACjB,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,GAAG,CAAC,GACrE,GAAG,SAAS,MAAM,GACd,GAAG,GACH,MAAM,GACV,KAAK,CAAC,GAAG;SAAG,CAAC,IAAI,sBAAsB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAAE;CACpE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AAE5B;;;;;GAKG;AACH,wBAAgB,4BAA4B,CACxC,KAAK,CAAC,SAAS,SAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAC9C,KAAK,CAAC,sBAAsB,SAAS,MAAM,GAAG,QAAQ,EAEtD,QAAQ,EAAE,SAAS,EACnB,MAAM,GAAE,6BAA6B,CAAC,sBAAsB,EAAE,aAAa,CAAM,GAClF,OAAO,CAAC,0BAA0B,CAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC,CAUxE;AAED;;;;;GAKG;AACH,wBAAgB,4BAA4B,CACxC,KAAK,CAAC,SAAS,SAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAC9C,KAAK,CAAC,sBAAsB,SAAS,MAAM,GAAG,QAAQ,EAEtD,QAAQ,EAAE,SAAS,EACnB,MAAM,GAAE,6BAA6B,CAAC,sBAAsB,EAAE,aAAa,CAAM,GAClF,OAAO,CAAC,0BAA0B,CAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC,CAYxE;AAED;;;;;GAKG;AACH,wBAAgB,0BAA0B,CACtC,KAAK,CAAC,SAAS,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EACjD,KAAK,CAAC,sBAAsB,SAAS,MAAM,GAAG,QAAQ,EAEtD,QAAQ,EAAE,SAAS,EACnB,MAAM,GAAE,6BAA6B,CAAC,sBAAsB,EAAE,WAAW,CAAM,GAChF,KAAK,CACJ,0BAA0B,CAAC,SAAS,EAAE,sBAAsB,CAAC,EAC7D,0BAA0B,CAAC,SAAS,EAAE,sBAAsB,CAAC,GACzD,0BAA0B,CAAC,SAAS,EAAE,sBAAsB,CAAC,CACpE,CAQA;AAgBD,8DAA8D;AAC9D,eAAO,MAAM,kBAAkB,qCAA+B,CAAC;AAE/D,8DAA8D;AAC9D,eAAO,MAAM,kBAAkB,qCAA+B,CAAC;AAE/D,4DAA4D;AAC5D,eAAO,MAAM,gBAAgB,mCAA6B,CAAC"}