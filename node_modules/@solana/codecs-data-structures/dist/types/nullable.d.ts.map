{"version": 3, "file": "nullable.d.ts", "sourceRoot": "", "sources": ["../../src/nullable.ts"], "names": [], "mappings": "AAAA,OAAO,EAEH,KAAK,EAGL,OAAO,EACP,OAAO,EAEP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAEhB,kBAAkB,EAGlB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAQhC,8CAA8C;AAC9C,MAAM,MAAM,mBAAmB,CAAC,OAAO,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IAC3F;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,EAAE,kBAAkB,GAAG,QAAQ,CAAC;IAE1C;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;CAC3B,CAAC;AAEF;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,EAC1D,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EACpC,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GACnF,gBAAgB,CAAC,KAAK,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;AACzC,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC9E,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAClC,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACrE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AACrC,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EACpB,MAAM,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GACjF,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAkCrC;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,EACxD,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,EAClC,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GACnF,gBAAgB,CAAC,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC9E,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAChC,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACrE,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACnC,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAClB,MAAM,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GACjF,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAyCnC;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EAAE,KAAK,SAAS,MAAM,EAC3E,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,EACvC,MAAM,EAAE,mBAAmB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GACjF,cAAc,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;AACnD,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,mBAAmB,CAAC,oBAAoB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC5E,cAAc,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;AAC5C,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,mBAAmB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACnE,iBAAiB,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,MAAM,CAAC,EAAE,mBAAmB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GAC/E,iBAAiB,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC"}