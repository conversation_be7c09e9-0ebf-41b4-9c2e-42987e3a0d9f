import { FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';
/**
 * Creates a void encoder.
 */
export declare function getUnitEncoder(): FixedSizeEncoder<void, 0>;
/**
 * Creates a void decoder.
 */
export declare function getUnitDecoder(): FixedSizeDecoder<void, 0>;
/**
 * Creates a void codec.
 */
export declare function getUnitCodec(): FixedSizeCodec<void, void, 0>;
//# sourceMappingURL=unit.d.ts.map