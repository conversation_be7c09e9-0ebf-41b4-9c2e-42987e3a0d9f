{"version": 3, "file": "struct.d.ts", "sourceRoot": "", "sources": ["../../src/struct.ts"], "names": [], "mappings": "AACA,OAAO,EACH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAE,iBAAiB,EAA2C,MAAM,SAAS,CAAC;AAErF,KAAK,MAAM,CAAC,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACnD,KAAK,YAAY,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;AAEtG,KAAK,wBAAwB,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,CAAC;KACnF,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK;CAC5G,CAAC,CAAC;AAEH,KAAK,wBAAwB,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,CAAC;KACnF,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK;CACxG,CAAC,CAAC;AAEH;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAChF,MAAM,EAAE,OAAO,GAChB,gBAAgB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;AACvD,wBAAgB,gBAAgB,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EACvE,MAAM,EAAE,OAAO,GAChB,mBAAmB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;AA4B1D;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAChF,MAAM,EAAE,OAAO,GAChB,gBAAgB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;AACvD,wBAAgB,gBAAgB,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EACvE,MAAM,EAAE,OAAO,GAChB,mBAAmB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;AAuB1D;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAC5E,MAAM,EAAE,OAAO,GAChB,cAAc,CACb,wBAAwB,CAAC,OAAO,CAAC,EACjC,wBAAwB,CAAC,OAAO,CAAC,GAAG,wBAAwB,CAAC,OAAO,CAAC,CACxE,CAAC;AACF,wBAAgB,cAAc,CAAC,KAAK,CAAC,OAAO,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EACnE,MAAM,EAAE,OAAO,GAChB,iBAAiB,CAChB,wBAAwB,CAAC,OAAO,CAAC,EACjC,wBAAwB,CAAC,OAAO,CAAC,GAAG,wBAAwB,CAAC,OAAO,CAAC,CACxE,CAAC"}