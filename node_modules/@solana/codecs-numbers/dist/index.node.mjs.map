{"version": 3, "sources": ["../src/assertions.ts", "../src/common.ts", "../src/utils.ts", "../src/f32.ts", "../src/f64.ts", "../src/i128.ts", "../src/i16.ts", "../src/i32.ts", "../src/i64.ts", "../src/i8.ts", "../src/short-u16.ts", "../src/u128.ts", "../src/u16.ts", "../src/u32.ts", "../src/u64.ts", "../src/u8.ts"], "names": ["<PERSON><PERSON>", "combineCodec", "createEncoder", "createDecoder"], "mappings": ";;;;AAKO,SAAS,6BACZ,CAAA,gBAAA,EACA,GACA,EAAA,GAAA,EACA,KACF,EAAA;AACE,EAAI,IAAA,KAAA,GAAQ,GAAO,IAAA,KAAA,GAAQ,GAAK,EAAA;AAC5B,IAAM,MAAA,IAAI,YAAY,yCAA2C,EAAA;AAAA,MAC7D,gBAAA;AAAA,MACA,GAAA;AAAA,MACA,GAAA;AAAA,MACA,KAAA;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;;;ACeY,IAAA,MAAA,qBAAAA,OAAL,KAAA;AACH,EAAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA;AACA,EAAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,CAAA;AAFQ,EAAAA,OAAAA,OAAAA,CAAAA;AAAA,CAAA,EAAA,MAAA,IAAA,EAAA,EAAA;ACLZ,SAAS,eAAe,MAAqC,EAAA;AACzD,EAAO,OAAA,MAAA,EAAQ,yBAAwB,KAAQ,GAAA,IAAA,CAAA;AACnD,CAAA;AAEO,SAAS,qBACZ,KAC8B,EAAA;AAC9B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,WAAW,KAAM,CAAA,IAAA;AAAA,IACjB,KAAA,CAAM,KAAc,EAAA,KAAA,EAAmB,MAAwB,EAAA;AAC3D,MAAA,IAAI,MAAM,KAAO,EAAA;AACb,QAA8B,6BAAA,CAAA,KAAA,CAAM,IAAM,EAAA,KAAA,CAAM,KAAM,CAAA,CAAC,GAAG,KAAM,CAAA,KAAA,CAAM,CAAC,CAAA,EAAG,KAAK,CAAA,CAAA;AAAA,OACnF;AACA,MAAA,MAAM,WAAc,GAAA,IAAI,WAAY,CAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AAC9C,MAAM,KAAA,CAAA,GAAA,CAAI,IAAI,QAAS,CAAA,WAAW,GAAG,KAAO,EAAA,cAAA,CAAe,KAAM,CAAA,MAAM,CAAC,CAAA,CAAA;AACxE,MAAA,KAAA,CAAM,GAAI,CAAA,IAAI,UAAW,CAAA,WAAW,GAAG,MAAM,CAAA,CAAA;AAC7C,MAAA,OAAO,SAAS,KAAM,CAAA,IAAA,CAAA;AAAA,KAC1B;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAEO,SAAS,qBACZ,KAC4B,EAAA;AAC5B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,WAAW,KAAM,CAAA,IAAA;AAAA,IACjB,IAAA,CAAK,KAAO,EAAA,MAAA,GAAS,CAAkB,EAAA;AACnC,MAAkC,iCAAA,CAAA,KAAA,CAAM,IAAM,EAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAC3D,MAAA,qCAAA,CAAsC,KAAM,CAAA,IAAA,EAAM,KAAM,CAAA,IAAA,EAAM,OAAO,MAAM,CAAA,CAAA;AAC3E,MAAM,MAAA,IAAA,GAAO,IAAI,QAAS,CAAA,aAAA,CAAc,OAAO,MAAQ,EAAA,KAAA,CAAM,IAAI,CAAC,CAAA,CAAA;AAClE,MAAO,OAAA,CAAC,KAAM,CAAA,GAAA,CAAI,IAAM,EAAA,cAAA,CAAe,KAAM,CAAA,MAAM,CAAC,CAAA,EAAG,MAAS,GAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AAAA,KAC9E;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAMA,SAAS,aAAA,CAAc,KAAwC,EAAA,MAAA,EAAiB,MAA8B,EAAA;AAC1G,EAAM,MAAA,WAAA,GAAc,KAAM,CAAA,UAAA,IAAc,MAAU,IAAA,CAAA,CAAA,CAAA;AAClD,EAAM,MAAA,WAAA,GAAc,UAAU,KAAM,CAAA,UAAA,CAAA;AACpC,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,KAAM,CAAA,WAAA,EAAa,cAAc,WAAW,CAAA,CAAA;AACpE,CAAA;;;ACnEO,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,UAAA,CAAW,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC9D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,UAAA,CAAW,GAAG,EAAE,CAAA;AAAA,EACxC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrD,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;ACjBtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,UAAA,CAAW,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC9D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,UAAA,CAAW,GAAG,EAAE,CAAA;AAAA,EACxC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDC,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;ACjBtD,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,oCAAoC,CAAI,GAAA,EAAA,EAAI,MAAO,CAAA,oCAAoC,CAAC,CAAA;AAAA,EACxG,GAAK,EAAA,CAAC,IAAM,EAAA,KAAA,EAAO,EAAO,KAAA;AACtB,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,SAAY,GAAA,mBAAA,CAAA;AAClB,IAAA,IAAA,CAAK,YAAY,UAAY,EAAA,MAAA,CAAO,KAAK,CAAA,IAAK,KAAK,EAAE,CAAA,CAAA;AACrD,IAAA,IAAA,CAAK,aAAa,WAAa,EAAA,MAAA,CAAO,KAAK,CAAA,GAAI,WAAW,EAAE,CAAA,CAAA;AAAA,GAChE;AAAA,EACA,IAAM,EAAA,EAAA;AACV,CAAC,EAAA;AAEE,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,GAAA,EAAK,CAAC,IAAA,EAAM,EAAO,KAAA;AACf,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,IAAO,GAAA,IAAA,CAAK,WAAY,CAAA,UAAA,EAAY,EAAE,CAAA,CAAA;AAC5C,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,YAAa,CAAA,WAAA,EAAa,EAAE,CAAA,CAAA;AAC/C,IAAA,OAAA,CAAQ,QAAQ,GAAO,IAAA,KAAA,CAAA;AAAA,GAC3B;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,EAAA;AACV,CAAC,EAAA;AAEQ,IAAA,YAAA,GAAe,CAAC,MAAA,GAA4B,EAAC,KACtDA,YAAa,CAAA,cAAA,CAAe,MAAM,CAAA,EAAG,cAAe,CAAA,MAAM,CAAC,EAAA;AC9BxD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,QAAQ,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,QAAQ,CAAC,CAAA;AAAA,EAC/C,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,QAAA,CAAS,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC5D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,QAAA,CAAS,GAAG,EAAE,CAAA;AAAA,EACtC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;AClBtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,YAAY,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,YAAY,CAAC,CAAA;AAAA,EACvD,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,QAAA,CAAS,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC5D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,QAAA,CAAS,GAAG,EAAE,CAAA;AAAA,EACtC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;AClBtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,oBAAoB,CAAI,GAAA,EAAA,EAAI,MAAO,CAAA,oBAAoB,CAAC,CAAA;AAAA,EACxE,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,WAAA,CAAY,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC/D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,WAAA,CAAY,GAAG,EAAE,CAAA;AAAA,EACzC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;ACnBhD,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,IAAM,EAAA,IAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,MAAM,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,MAAM,CAAC,CAAA;AAAA,EAC3C,GAAA,EAAK,CAAC,IAAM,EAAA,KAAA,KAAU,KAAK,OAAQ,CAAA,CAAA,EAAG,MAAO,CAAA,KAAK,CAAC,CAAA;AAAA,EACnD,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,GAAK,EAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,OAAA,CAAQ,CAAC,CAAA;AAAA,EAC3B,IAAM,EAAA,IAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAa,MACtBA,YAAAA,CAAa,YAAa,EAAA,EAAG,cAAc,EAAA;ACHlC,IAAA,kBAAA,GAAqB,MAC9BC,aAAc,CAAA;AAAA,EACV,gBAAA,EAAkB,CAAC,KAAmC,KAAA;AAClD,IAAI,IAAA,KAAA,IAAS,KAAmB,OAAA,CAAA,CAAA;AAChC,IAAI,IAAA,KAAA,IAAS,OAA2B,OAAA,CAAA,CAAA;AACxC,IAAO,OAAA,CAAA,CAAA;AAAA,GACX;AAAA,EACA,OAAS,EAAA,CAAA;AAAA,EACT,KAAO,EAAA,CAAC,KAAwB,EAAA,KAAA,EAAmB,MAA2B,KAAA;AAC1E,IAA8B,6BAAA,CAAA,UAAA,EAAY,CAAG,EAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AACzD,IAAM,MAAA,aAAA,GAAgB,CAAC,CAAC,CAAA,CAAA;AACxB,IAAS,KAAA,IAAA,EAAA,GAAK,CAAK,IAAA,EAAA,IAAM,CAAG,EAAA;AAExB,MAAA,MAAM,YAAe,GAAA,MAAA,CAAO,KAAK,CAAA,IAAM,EAAK,GAAA,CAAA,CAAA;AAC5C,MAAA,IAAI,iBAAiB,CAAG,EAAA;AAEpB,QAAA,MAAA;AAAA,OACJ;AAEA,MAAA,MAAM,gBAAgB,GAAY,GAAA,YAAA,CAAA;AAClC,MAAA,aAAA,CAAc,EAAE,CAAI,GAAA,aAAA,CAAA;AACpB,MAAA,IAAI,KAAK,CAAG,EAAA;AAER,QAAc,aAAA,CAAA,EAAA,GAAK,CAAC,CAAK,IAAA,GAAA,CAAA;AAAA,OAC7B;AAAA,KACJ;AACA,IAAM,KAAA,CAAA,GAAA,CAAI,eAAe,MAAM,CAAA,CAAA;AAC/B,IAAA,OAAO,SAAS,aAAc,CAAA,MAAA,CAAA;AAAA,GAClC;AACJ,CAAC,EAAA;AAMQ,IAAA,kBAAA,GAAqB,MAC9BC,aAAc,CAAA;AAAA,EACV,OAAS,EAAA,CAAA;AAAA,EACT,IAAA,EAAM,CAAC,KAAA,EAAwC,MAA6B,KAAA;AACxE,IAAA,IAAI,KAAQ,GAAA,CAAA,CAAA;AACZ,IAAA,IAAI,SAAY,GAAA,CAAA,CAAA;AAChB,IAAA,OAAO,EAAE,SAAW,EAAA;AAChB,MAAA,MAAM,YAAY,SAAY,GAAA,CAAA,CAAA;AAC9B,MAAM,MAAA,WAAA,GAAc,KAAM,CAAA,MAAA,GAAS,SAAS,CAAA,CAAA;AAC5C,MAAA,MAAM,gBAAgB,GAAY,GAAA,WAAA,CAAA;AAElC,MAAA,KAAA,IAAS,iBAAkB,SAAY,GAAA,CAAA,CAAA;AACvC,MAAK,IAAA,CAAA,WAAA,GAAc,SAAgB,CAAG,EAAA;AAElC,QAAA,MAAA;AAAA,OACJ;AAAA,KACJ;AACA,IAAO,OAAA,CAAC,KAAO,EAAA,MAAA,GAAS,SAAS,CAAA,CAAA;AAAA,GACrC;AACJ,CAAC,EAAA;AAWE,IAAM,mBAAmB,MAC5BF,YAAAA,CAAa,kBAAmB,EAAA,EAAG,oBAAoB,EAAA;AC9EpD,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,CAAC,EAAI,EAAA,MAAA,CAAO,oCAAoC,CAAC,CAAA;AAAA,EACxD,GAAK,EAAA,CAAC,IAAM,EAAA,KAAA,EAAO,EAAO,KAAA;AACtB,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,SAAY,GAAA,mBAAA,CAAA;AAClB,IAAA,IAAA,CAAK,aAAa,UAAY,EAAA,MAAA,CAAO,KAAK,CAAA,IAAK,KAAK,EAAE,CAAA,CAAA;AACtD,IAAA,IAAA,CAAK,aAAa,WAAa,EAAA,MAAA,CAAO,KAAK,CAAA,GAAI,WAAW,EAAE,CAAA,CAAA;AAAA,GAChE;AAAA,EACA,IAAM,EAAA,EAAA;AACV,CAAC,EAAA;AAEE,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,GAAA,EAAK,CAAC,IAAA,EAAM,EAAO,KAAA;AACf,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,IAAO,GAAA,IAAA,CAAK,YAAa,CAAA,UAAA,EAAY,EAAE,CAAA,CAAA;AAC7C,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,YAAa,CAAA,WAAA,EAAa,EAAE,CAAA,CAAA;AAC/C,IAAA,OAAA,CAAQ,QAAQ,GAAO,IAAA,KAAA,CAAA;AAAA,GAC3B;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,EAAA;AACV,CAAC,EAAA;AAEQ,IAAA,YAAA,GAAe,CAAC,MAAA,GAA4B,EAAC,KACtDA,YAAa,CAAA,cAAA,CAAe,MAAM,CAAA,EAAG,cAAe,CAAA,MAAM,CAAC,EAAA;AC9BxD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,QAAQ,CAAC,CAAA;AAAA,EAC3B,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,SAAA,CAAU,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC7D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,SAAA,CAAU,GAAG,EAAE,CAAA;AAAA,EACvC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;AClBtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,YAAY,CAAC,CAAA;AAAA,EAC/B,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,SAAA,CAAU,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC7D,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,SAAA,CAAU,GAAG,EAAE,CAAA;AAAA,EACvC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;AClBtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,EAAI,EAAA,MAAA,CAAO,oBAAoB,CAAC,CAAA;AAAA,EACxC,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,YAAA,CAAa,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAChE,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,YAAA,CAAa,GAAG,EAAE,CAAA;AAAA,EAC1C,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC,EAAA;ACnBhD,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,IAAM,EAAA,IAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,MAAM,CAAC,CAAA;AAAA,EACzB,GAAA,EAAK,CAAC,IAAM,EAAA,KAAA,KAAU,KAAK,QAAS,CAAA,CAAA,EAAG,MAAO,CAAA,KAAK,CAAC,CAAA;AAAA,EACpD,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEQ,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,GAAK,EAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,QAAA,CAAS,CAAC,CAAA;AAAA,EAC5B,IAAM,EAAA,IAAA;AAAA,EACN,IAAM,EAAA,CAAA;AACV,CAAC,EAAA;AAEE,IAAM,aAAa,MACtBA,YAAAA,CAAa,YAAa,EAAA,EAAG,cAAc", "file": "index.node.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE, SolanaError } from '@solana/errors';\n\n/**\n * Asserts that a given number is between a given range.\n */\nexport function assertNumberIsBetweenForCodec(\n    codecDescription: string,\n    min: bigint | number,\n    max: bigint | number,\n    value: bigint | number,\n) {\n    if (value < min || value > max) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE, {\n            codecDescription,\n            max,\n            min,\n            value,\n        });\n    }\n}\n", "import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\n/** Defines a encoder for numbers and bigints. */\nexport type NumberEncoder = Encoder<bigint | number>;\n\n/** Defines a fixed-size encoder for numbers and bigints. */\nexport type FixedSizeNumberEncoder<TSize extends number = number> = FixedSizeEncoder<bigint | number, TSize>;\n\n/** Defines a decoder for numbers and bigints. */\nexport type NumberDecoder = Decoder<bigint> | Decoder<number>;\n\n/** Defines a fixed-size decoder for numbers and bigints. */\nexport type FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<bigint, TSize>\n    | FixedSizeDecoder<number, TSize>;\n\n/** Defines a codec for numbers and bigints. */\nexport type NumberCodec = Codec<bigint | number, bigint> | Codec<bigint | number, number>;\n\n/** Defines a fixed-size codec for numbers and bigints. */\nexport type FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<bigint | number, bigint, TSize>\n    | FixedSizeCodec<bigint | number, number, TSize>;\n\n/** Defines the config for number codecs that use more than one byte. */\nexport type NumberCodecConfig = {\n    /**\n     * Whether the serializer should use little-endian or big-endian encoding.\n     * @defaultValue `Endian.Little`\n     */\n    endian?: Endian;\n};\n\n/** Defines the endianness of a number serializer. */\nexport enum Endian {\n    Little,\n    Big,\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    Offset,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\nimport { Endian, NumberCodecConfig } from './common';\n\ntype NumberFactorySharedInput<TSize extends number> = {\n    config?: NumberCodecConfig;\n    name: string;\n    size: TSize;\n};\n\ntype NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    range?: [bigint | number, bigint | number];\n    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;\n};\n\ntype NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    get: (view: DataView, littleEndian?: boolean) => TTo;\n};\n\nfunction isLittleEndian(config?: NumberCodecConfig): boolean {\n    return config?.endian === Endian.Big ? false : true;\n}\n\nexport function numberEncoderFactory<TFrom extends bigint | number, TSize extends number>(\n    input: NumberFactoryEncoderInput<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: input.size,\n        write(value: TFrom, bytes: Uint8Array, offset: Offset): Offset {\n            if (input.range) {\n                assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);\n            }\n            const arrayBuffer = new ArrayBuffer(input.size);\n            input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));\n            bytes.set(new Uint8Array(arrayBuffer), offset);\n            return offset + input.size;\n        },\n    });\n}\n\nexport function numberDecoderFactory<TTo extends bigint | number, TSize extends number>(\n    input: NumberFactoryDecoderInput<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: input.size,\n        read(bytes, offset = 0): [TTo, number] {\n            assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);\n            assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);\n            const view = new DataView(toArrayBuffer(bytes, offset, input.size));\n            return [input.get(view, isLittleEndian(input.config)), offset + input.size];\n        },\n    });\n}\n\n/**\n * Helper function to ensure that the ArrayBuffer is converted properly from a Uint8Array\n * Source: https://stackoverflow.com/questions/37228285/uint8array-to-arraybuffer\n */\nfunction toArrayBuffer(bytes: ReadonlyUint8Array | Uint8Array, offset?: number, length?: number): ArrayBuffer {\n    const bytesOffset = bytes.byteOffset + (offset ?? 0);\n    const bytesLength = length ?? bytes.byteLength;\n    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'f32',\n        set: (view, value, le) => view.setFloat32(0, Number(value), le),\n        size: 4,\n    });\n\nexport const getF32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat32(0, le),\n        name: 'f32',\n        size: 4,\n    });\n\nexport const getF32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getF32Encoder(config), getF32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'f64',\n        set: (view, value, le) => view.setFloat64(0, Number(value), le),\n        size: 8,\n    });\n\nexport const getF64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat64(0, le),\n        name: 'f64',\n        size: 8,\n    });\n\nexport const getF64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 8> =>\n    combineCodec(getF64Encoder(config), getF64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'i128',\n        range: [-BigInt('0x7fffffffffffffffffffffffffffffff') - 1n, BigInt('0x7fffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigInt64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getI128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigInt64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'i128',\n        size: 16,\n    });\n\nexport const getI128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 16> =>\n    combineCodec(getI128Encoder(config), getI128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'i16',\n        range: [-Number('0x7fff') - 1, Number('0x7fff')],\n        set: (view, value, le) => view.setInt16(0, Number(value), le),\n        size: 2,\n    });\n\nexport const getI16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt16(0, le),\n        name: 'i16',\n        size: 2,\n    });\n\nexport const getI16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 2> =>\n    combineCodec(getI16Encoder(config), getI16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'i32',\n        range: [-Number('0x7fffffff') - 1, Number('0x7fffffff')],\n        set: (view, value, le) => view.setInt32(0, Number(value), le),\n        size: 4,\n    });\n\nexport const getI32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt32(0, le),\n        name: 'i32',\n        size: 4,\n    });\n\nexport const getI32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getI32Encoder(config), getI32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'i64',\n        range: [-BigInt('0x7fffffffffffffff') - 1n, BigInt('0x7fffffffffffffff')],\n        set: (view, value, le) => view.setBigInt64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getI64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigInt64(0, le),\n        name: 'i64',\n        size: 8,\n    });\n\nexport const getI64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 8> =>\n    combineCodec(getI64Encoder(config), getI64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI8Encoder = (): FixedSizeEncoder<bigint | number, 1> =>\n    numberEncoderFactory({\n        name: 'i8',\n        range: [-Number('0x7f') - 1, Number('0x7f')],\n        set: (view, value) => view.setInt8(0, Number(value)),\n        size: 1,\n    });\n\nexport const getI8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getInt8(0),\n        name: 'i8',\n        size: 1,\n    });\n\nexport const getI8Codec = (): FixedSizeCodec<bigint | number, number, 1> =>\n    combineCodec(getI8Encoder(), getI8Decoder());\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Offset,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\n\n/**\n * Encodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Encoder = (): VariableSizeEncoder<bigint | number> =>\n    createEncoder({\n        getSizeFromValue: (value: bigint | number): number => {\n            if (value <= 0b01111111) return 1;\n            if (value <= 0b0011111111111111) return 2;\n            return 3;\n        },\n        maxSize: 3,\n        write: (value: bigint | number, bytes: Uint8Array, offset: Offset): Offset => {\n            assertNumberIsBetweenForCodec('shortU16', 0, 65535, value);\n            const shortU16Bytes = [0];\n            for (let ii = 0; ; ii += 1) {\n                // Shift the bits of the value over such that the next 7 bits are at the right edge.\n                const alignedValue = Number(value) >> (ii * 7);\n                if (alignedValue === 0) {\n                    // No more bits to consume.\n                    break;\n                }\n                // Extract those 7 bits using a mask.\n                const nextSevenBits = 0b1111111 & alignedValue;\n                shortU16Bytes[ii] = nextSevenBits;\n                if (ii > 0) {\n                    // Set the continuation bit of the previous slice.\n                    shortU16Bytes[ii - 1] |= 0b10000000;\n                }\n            }\n            bytes.set(shortU16Bytes, offset);\n            return offset + shortU16Bytes.length;\n        },\n    });\n\n/**\n * Decodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Decoder = (): VariableSizeDecoder<number> =>\n    createDecoder({\n        maxSize: 3,\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset): [number, Offset] => {\n            let value = 0;\n            let byteCount = 0;\n            while (++byteCount) {\n                const byteIndex = byteCount - 1;\n                const currentByte = bytes[offset + byteIndex];\n                const nextSevenBits = 0b1111111 & currentByte;\n                // Insert the next group of seven bits into the correct slot of the output value.\n                value |= nextSevenBits << (byteIndex * 7);\n                if ((currentByte & 0b10000000) === 0) {\n                    // This byte does not have its continuation bit set. We're done.\n                    break;\n                }\n            }\n            return [value, offset + byteCount];\n        },\n    });\n\n/**\n * Encodes and decodes short u16 numbers.\n *\n * Short u16 numbers are the same as u16, but serialized with 1 to 3 bytes.\n * If the value is above 0x7f, the top bit is set and the remaining\n * value is stored in the next bytes. Each byte follows the same\n * pattern until the 3rd byte. The 3rd byte, if needed, uses\n * all 8 bits to store the last byte of the original value.\n */\nexport const getShortU16Codec = (): VariableSizeCodec<bigint | number, number> =>\n    combineCodec(getShortU16Encoder(), getShortU16Decoder());\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'u128',\n        range: [0n, BigInt('0xffffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigUint64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getU128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigUint64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'u128',\n        size: 16,\n    });\n\nexport const getU128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 16> =>\n    combineCodec(getU128Encoder(config), getU128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'u16',\n        range: [0, Number('0xffff')],\n        set: (view, value, le) => view.setUint16(0, Number(value), le),\n        size: 2,\n    });\n\nexport const getU16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint16(0, le),\n        name: 'u16',\n        size: 2,\n    });\n\nexport const getU16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 2> =>\n    combineCodec(getU16Encoder(config), getU16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'u32',\n        range: [0, Number('0xffffffff')],\n        set: (view, value, le) => view.setUint32(0, Number(value), le),\n        size: 4,\n    });\n\nexport const getU32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint32(0, le),\n        name: 'u32',\n        size: 4,\n    });\n\nexport const getU32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getU32Encoder(config), getU32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'u64',\n        range: [0n, BigInt('0xffffffffffffffff')],\n        set: (view, value, le) => view.setBigUint64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getU64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigUint64(0, le),\n        name: 'u64',\n        size: 8,\n    });\n\nexport const getU64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 8> =>\n    combineCodec(getU64Encoder(config), getU64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU8Encoder = (): FixedSizeEncoder<bigint | number, 1> =>\n    numberEncoderFactory({\n        name: 'u8',\n        range: [0, Number('0xff')],\n        set: (view, value) => view.setUint8(0, Number(value)),\n        size: 1,\n    });\n\nexport const getU8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getUint8(0),\n        name: 'u8',\n        size: 1,\n    });\n\nexport const getU8Codec = (): FixedSizeCodec<bigint | number, number, 1> =>\n    combineCodec(getU8Encoder(), getU8Decoder());\n"]}