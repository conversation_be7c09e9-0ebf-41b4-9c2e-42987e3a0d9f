{"version": 3, "file": "transform-codec.d.ts", "sourceRoot": "", "sources": ["../../src/transform-codec.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAEhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3D;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,SAAS,MAAM,EACrE,OAAO,EAAE,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrC,wBAAgB,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAC/C,OAAO,EAAE,mBAAmB,CAAC,QAAQ,CAAC,EACtC,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACjC,wBAAgB,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAC/C,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAC1B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAarB;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,SAAS,MAAM,EACjE,OAAO,EAAE,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,EACxC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAC3C,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC/B,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAC3C,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EACxB,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,OAAO,CAAC,MAAM,CAAC,CAAC;AAcnB;;GAEG;AACH,wBAAgB,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EAAE,KAAK,SAAS,MAAM,EACpG,KAAK,EAAE,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,EAC3C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACxC,wBAAgB,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EAC9E,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,EACvC,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACpC,wBAAgB,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EAC9E,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,EAC3B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACxB,wBAAgB,cAAc,CAC1B,QAAQ,EACR,QAAQ,EACR,MAAM,SAAS,QAAQ,EACvB,MAAM,SAAS,QAAQ,EACvB,KAAK,SAAS,MAAM,EAEpB,KAAK,EAAE,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,EAC9C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3C,wBAAgB,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAC/F,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,EAC1C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACvC,wBAAgB,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAC/F,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAC9B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACvF,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC"}