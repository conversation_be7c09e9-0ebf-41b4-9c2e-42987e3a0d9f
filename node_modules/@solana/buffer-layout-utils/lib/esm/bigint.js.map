{"version": 3, "file": "bigint.js", "sourceRoot": "", "sources": ["../../src/bigint.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAU,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC/E,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,MAAM,CAAC,MAAM,MAAM,GACf,CAAC,MAAc,EAAE,EAAE,CACnB,CAAC,QAAiB,EAAkB,EAAE;IAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACtC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAEhD,MAAM,YAAY,GAAG,MAA2C,CAAC;IAEjE,YAAY,CAAC,MAAM,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,EAAE;QACrD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnC,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,YAAY,CAAC,MAAM,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,MAAc,EAAE,EAAE;QACrE,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEN,MAAM,CAAC,MAAM,QAAQ,GACjB,CAAC,MAAc,EAAE,EAAE,CACnB,CAAC,QAAiB,EAAkB,EAAE;IAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACtC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAEhD,MAAM,YAAY,GAAG,MAA2C,CAAC;IAEjE,YAAY,CAAC,MAAM,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,EAAE;QACrD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnC,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,YAAY,CAAC,MAAM,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,MAAc,EAAE,EAAE;QACrE,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEN,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAE7B,MAAM,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAEjC,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAE/B,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEnC,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAE/B,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEnC,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAE/B,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC"}