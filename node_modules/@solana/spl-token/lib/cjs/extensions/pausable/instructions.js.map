{"version": 3, "file": "instructions.js", "sourceRoot": "", "sources": ["../../../../src/extensions/pausable/instructions.ts"], "names": [], "mappings": ";;;AAkCA,8FAqBC;AAiBD,wDAqBC;AAiBD,0DAqBC;AAnID,yDAAmD;AACnD,qEAAwD;AAExD,6CAAoE;AACpE,qDAAsF;AACtF,+CAAmE;AACnE,0DAA+D;AAC/D,gEAA4D;AAE5D,IAAY,mBAIX;AAJD,WAAY,mBAAmB;IAC3B,yEAAc,CAAA;IACd,+DAAS,CAAA;IACT,iEAAU,CAAA;AACd,CAAC,EAJW,mBAAmB,mCAAnB,mBAAmB,QAI9B;AAQY,QAAA,uCAAuC,GAAG,IAAA,sBAAM,EAA0C;IACnG,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,qBAAqB,CAAC;IACzB,IAAA,+BAAS,EAAC,WAAW,CAAC;CACzB,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,SAAgB,yCAAyC,CACrD,IAAe,EACf,SAA2B,EAC3B,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IACD,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IAEnE,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,+CAAuC,CAAC,IAAI,CAAC,CAAC;IACxE,+CAAuC,CAAC,MAAM,CAC1C;QACI,WAAW,EAAE,2BAAgB,CAAC,iBAAiB;QAC/C,mBAAmB,EAAE,mBAAmB,CAAC,UAAU;QACnD,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,mBAAS,CAAC,OAAO;KAC5C,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC;AAOY,QAAA,oBAAoB,GAAG,IAAA,sBAAM,EAAuB,CAAC,IAAA,kBAAE,EAAC,aAAa,CAAC,EAAE,IAAA,kBAAE,EAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAEjH;;;;;;;GAOG;AACH,SAAgB,sBAAsB,CAClC,IAAe,EACf,SAAoB,EACpB,eAAuC,EAAE,EACzC,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IACD,MAAM,IAAI,GAAG,IAAA,wBAAU,EAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAExG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,4BAAoB,CAAC,IAAI,CAAC,CAAC;IACrD,4BAAoB,CAAC,MAAM,CACvB;QACI,WAAW,EAAE,2BAAgB,CAAC,iBAAiB;QAC/C,mBAAmB,EAAE,mBAAmB,CAAC,KAAK;KACjD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC;AAOY,QAAA,qBAAqB,GAAG,IAAA,sBAAM,EAAwB,CAAC,IAAA,kBAAE,EAAC,aAAa,CAAC,EAAE,IAAA,kBAAE,EAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAEnH;;;;;;;GAOG;AACH,SAAgB,uBAAuB,CACnC,IAAe,EACf,SAAoB,EACpB,eAAuC,EAAE,EACzC,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IACD,MAAM,IAAI,GAAG,IAAA,wBAAU,EAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAExG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,6BAAqB,CAAC,IAAI,CAAC,CAAC;IACtD,6BAAqB,CAAC,MAAM,CACxB;QACI,WAAW,EAAE,2BAAgB,CAAC,iBAAiB;QAC/C,mBAAmB,EAAE,mBAAmB,CAAC,MAAM;KAClD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC"}