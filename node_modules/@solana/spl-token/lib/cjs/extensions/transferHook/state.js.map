{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/transferHook/state.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAwBA,0CAOC;AAgBD,wDAOC;AAED,gEAGC;AA4CD,oDAGC;AAGD,0DAsCC;AAnJD,yDAA2E;AAE3E,0DAAsE;AAEtE,6CAA4C;AAC5C,qEAAmE;AAEnE,+CAAmE;AACnE,yCAAyC;AACzC,mDAAmD;AAUnD,iEAAiE;AACpD,QAAA,kBAAkB,GAAG,IAAA,sBAAM,EAAe,CAAC,IAAA,+BAAS,EAAC,WAAW,CAAC,EAAE,IAAA,+BAAS,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAE5F,QAAA,kBAAkB,GAAG,0BAAkB,CAAC,IAAI,CAAC;AAE1D,SAAgB,eAAe,CAAC,IAAU;IACtC,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACjF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,0BAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAWD,yEAAyE;AAC5D,QAAA,yBAAyB,GAAG,IAAA,sBAAM,EAAsB,CAAC,IAAA,0BAAI,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAEhF,QAAA,0BAA0B,GAAG,iCAAyB,CAAC,IAAI,CAAC;AAEzE,SAAgB,sBAAsB,CAAC,OAAgB;IACnD,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,mBAAmB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3F,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,iCAAyB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,SAAgB,0BAA0B,CAAC,IAAe,EAAE,SAAoB;IAC5E,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpE,OAAO,mBAAS,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC;AAUD,2DAA2D;AAC9C,QAAA,sBAAsB,GAAG,IAAA,sBAAM,EAAmB;IAC3D,IAAA,kBAAE,EAAC,eAAe,CAAC;IACnB,IAAA,oBAAI,EAAC,EAAE,EAAE,eAAe,CAAC;IACzB,IAAA,0BAAI,EAAC,UAAU,CAAC;IAChB,IAAA,0BAAI,EAAC,YAAY,CAAC;CACrB,CAAC,CAAC;AAOH,2FAA2F;AAC9E,QAAA,0BAA0B,GAAG,IAAA,sBAAM,EAAuB;IACnE,IAAA,mBAAG,EAAC,OAAO,CAAC;IACZ,IAAA,mBAAG,EAAmB,8BAAsB,EAAE,IAAA,sBAAM,EAAC,8BAAsB,CAAC,IAAI,CAAC,EAAE,eAAe,CAAC;CACtG,CAAC,CAAC;AASH,sEAAsE;AACzD,QAAA,iCAAiC,GAAG,IAAA,sBAAM,EAA8B;IACjF,IAAA,yBAAG,EAAC,0BAA0B,CAAC;IAC/B,IAAA,mBAAG,EAAC,QAAQ,CAAC;IACb,kCAA0B,CAAC,SAAS,CAAC,mBAAmB,CAAC;CAC5D,CAAC,CAAC;AAEH,gGAAgG;AAChG,SAAgB,oBAAoB,CAAC,OAA4B;IAC7D,MAAM,iBAAiB,GAAG,yCAAiC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC;IACnG,OAAO,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC;AAED,6EAA6E;AAC7E,SAAsB,uBAAuB,CACzC,UAAsB,EACtB,SAA2B,EAC3B,aAA4B,EAC5B,eAAuB,EACvB,qBAAgC;;QAEhC,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO;gBACH,MAAM,EAAE,IAAI,mBAAS,CAAC,SAAS,CAAC,aAAa,CAAC;gBAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;aACnC,CAAC;QACN,CAAC;aAAM,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,MAAM,IAAA,gCAAgB,EAAC,SAAS,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;YAC3G,OAAO;gBACH,MAAM;gBACN,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;aACnC,CAAC;QACN,CAAC;QAED,IAAI,SAAS,GAAG,mBAAS,CAAC,OAAO,CAAC;QAElC,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAChC,SAAS,GAAG,qBAAqB,CAAC;QACtC,CAAC;aAAM,CAAC;YACJ,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACxD,IAAI,aAAa,CAAC,MAAM,IAAI,YAAY,EAAE,CAAC;gBACvC,MAAM,IAAI,4CAAgC,EAAE,CAAC;YACjD,CAAC;YACD,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;QACnD,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAA,sBAAW,EAAC,SAAS,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QACrG,MAAM,MAAM,GAAG,mBAAS,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAErE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC;IACtF,CAAC;CAAA"}