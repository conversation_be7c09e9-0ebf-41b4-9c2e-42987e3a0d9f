{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/metadataPointer/state.ts"], "names": [], "mappings": ";;;AAsBA,0DAaC;AAnCD,yDAA+C;AAC/C,qEAAwD;AACxD,6CAA4C;AAE5C,0DAAsE;AAUtE,oEAAoE;AACvD,QAAA,qBAAqB,GAAG,IAAA,sBAAM,EAAuD;IAC9F,IAAA,+BAAS,EAAC,WAAW,CAAC;IACtB,IAAA,+BAAS,EAAC,iBAAiB,CAAC;CAC/B,CAAC,CAAC;AAEU,QAAA,qBAAqB,GAAG,6BAAqB,CAAC,IAAI,CAAC;AAEhE,SAAgB,uBAAuB,CAAC,IAAU;IAC9C,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACpF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,6BAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEnF,wCAAwC;QACxC,OAAO;YACH,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACjE,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,mBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;SACtF,CAAC;IACN,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}