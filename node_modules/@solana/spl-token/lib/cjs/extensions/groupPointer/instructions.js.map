{"version": 3, "file": "instructions.js", "sourceRoot": "", "sources": ["../../../../src/extensions/groupPointer/instructions.ts"], "names": [], "mappings": ";;;AAqCA,0FAuBC;AAaD,kFAwBC;AAjGD,yDAAmD;AACnD,qEAAwD;AAExD,6CAAoE;AACpE,qDAAsF;AACtF,+CAAmE;AACnE,0DAA+D;AAC/D,gEAA4D;AAE5D,IAAY,uBAGX;AAHD,WAAY,uBAAuB;IAC/B,iFAAc,CAAA;IACd,yEAAU,CAAA;AACd,CAAC,EAHW,uBAAuB,uCAAvB,uBAAuB,QAGlC;AAEY,QAAA,0BAA0B,GAAG,IAAA,sBAAM,EAK7C;IACC,kBAAkB;IAClB,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,yBAAyB,CAAC;IAC7B,IAAA,+BAAS,EAAC,WAAW,CAAC;IACtB,IAAA,+BAAS,EAAC,cAAc,CAAC;CAC5B,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,SAAgB,uCAAuC,CACnD,IAAe,EACf,SAA2B,EAC3B,YAA8B,EAC9B,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IACD,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IAEnE,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,kCAA0B,CAAC,IAAI,CAAC,CAAC;IAC3D,kCAA0B,CAAC,MAAM,CAC7B;QACI,WAAW,EAAE,2BAAgB,CAAC,qBAAqB;QACnD,uBAAuB,EAAE,uBAAuB,CAAC,UAAU;QAC3D,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,mBAAS,CAAC,OAAO;QACzC,YAAY,EAAE,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,mBAAS,CAAC,OAAO;KAClD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC;AAEY,QAAA,sBAAsB,GAAG,IAAA,sBAAM,EAIzC;IACC,kBAAkB;IAClB,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,yBAAyB,CAAC;IAC7B,IAAA,+BAAS,EAAC,cAAc,CAAC;CAC5B,CAAC,CAAC;AAEH,SAAgB,mCAAmC,CAC/C,IAAe,EACf,SAAoB,EACpB,YAA8B,EAC9B,eAAuC,EAAE,EACzC,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IAED,MAAM,IAAI,GAAG,IAAA,wBAAU,EAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAExG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,8BAAsB,CAAC,IAAI,CAAC,CAAC;IACvD,8BAAsB,CAAC,MAAM,CACzB;QACI,WAAW,EAAE,2BAAgB,CAAC,qBAAqB;QACnD,uBAAuB,EAAE,uBAAuB,CAAC,MAAM;QACvD,YAAY,EAAE,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,mBAAS,CAAC,OAAO;KAClD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC"}