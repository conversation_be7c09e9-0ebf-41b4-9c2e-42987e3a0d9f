{"version": 3, "sources": ["../src/assertions.ts", "../src/baseX.ts", "../src/base10.ts", "../src/base16.ts", "../src/base58.ts", "../src/baseX-reslice.ts", "../src/base64.ts", "../src/null-characters.ts", "../../text-encoding-impl/src/index.node.ts", "../src/utf8.ts"], "names": ["alphabet", "createEncoder", "SolanaError", "SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE", "createDecoder", "combineCodec", "TextDecoder", "TextEncoder"], "mappings": ";;;;AAKO,SAAS,qBAAsBA,CAAAA,SAAAA,EAAkB,SAAmB,EAAA,UAAA,GAAa,SAAW,EAAA;AAC/F,EAAI,IAAA,CAAC,UAAU,KAAM,CAAA,IAAI,OAAO,CAAKA,EAAAA,EAAAA,SAAQ,CAAK,GAAA,CAAA,CAAC,CAAG,EAAA;AAClD,IAAM,MAAA,IAAI,YAAY,6CAA+C,EAAA;AAAA,MACjE,QAAAA,EAAAA,SAAAA;AAAA,MACA,MAAMA,SAAS,CAAA,MAAA;AAAA,MACf,KAAO,EAAA,UAAA;AAAA,KACV,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;ACGa,IAAA,eAAA,GAAkB,CAACA,SAAkD,KAAA;AAC9E,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,CAAC,KAA0B,KAAA;AACzC,MAAM,MAAA,CAAC,eAAe,SAAS,CAAA,GAAI,uBAAuB,KAAOA,EAAAA,SAAAA,CAAS,CAAC,CAAC,CAAA,CAAA;AAC5E,MAAI,IAAA,CAAC,SAAW,EAAA,OAAO,KAAM,CAAA,MAAA,CAAA;AAE7B,MAAM,MAAA,YAAA,GAAe,kBAAmB,CAAA,SAAA,EAAWA,SAAQ,CAAA,CAAA;AAC3D,MAAO,OAAA,aAAA,CAAc,SAAS,IAAK,CAAA,IAAA,CAAK,aAAa,QAAS,CAAA,EAAE,CAAE,CAAA,MAAA,GAAS,CAAC,CAAA,CAAA;AAAA,KAChF;AAAA,IACA,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAEhC,MAAA,qBAAA,CAAsBA,WAAU,KAAK,CAAA,CAAA;AACrC,MAAI,IAAA,KAAA,KAAU,IAAW,OAAA,MAAA,CAAA;AAGzB,MAAM,MAAA,CAAC,eAAe,SAAS,CAAA,GAAI,uBAAuB,KAAOA,EAAAA,SAAAA,CAAS,CAAC,CAAC,CAAA,CAAA;AAC5E,MAAA,IAAI,CAAC,SAAW,EAAA;AACZ,QAAM,KAAA,CAAA,GAAA,CAAI,IAAI,UAAW,CAAA,aAAA,CAAc,MAAM,CAAE,CAAA,IAAA,CAAK,CAAC,CAAA,EAAG,MAAM,CAAA,CAAA;AAC9D,QAAA,OAAO,SAAS,aAAc,CAAA,MAAA,CAAA;AAAA,OAClC;AAGA,MAAI,IAAA,YAAA,GAAe,kBAAmB,CAAA,SAAA,EAAWA,SAAQ,CAAA,CAAA;AAGzD,MAAA,MAAM,YAAsB,EAAC,CAAA;AAC7B,MAAA,OAAO,eAAe,EAAI,EAAA;AACtB,QAAA,SAAA,CAAU,OAAQ,CAAA,MAAA,CAAO,YAAe,GAAA,IAAI,CAAC,CAAA,CAAA;AAC7C,QAAgB,YAAA,IAAA,IAAA,CAAA;AAAA,OACpB;AAEA,MAAM,MAAA,UAAA,GAAa,CAAC,GAAG,KAAM,CAAA,aAAA,CAAc,MAAM,CAAA,CAAE,IAAK,CAAA,CAAC,CAAG,EAAA,GAAG,SAAS,CAAA,CAAA;AACxE,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA,CAAA;AAC5B,MAAA,OAAO,SAAS,UAAW,CAAA,MAAA,CAAA;AAAA,KAC/B;AAAA,GACH,CAAA,CAAA;AACL,EAAA;AAOa,IAAA,eAAA,GAAkB,CAACA,SAAkD,KAAA;AAC9E,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,IAAA,CAAK,UAAU,MAA0B,EAAA;AACrC,MAAA,MAAM,QAAQ,MAAW,KAAA,CAAA,GAAI,QAAW,GAAA,QAAA,CAAS,MAAM,MAAM,CAAA,CAAA;AAC7D,MAAA,IAAI,MAAM,MAAW,KAAA,CAAA,EAAU,OAAA,CAAC,IAAI,CAAC,CAAA,CAAA;AAGrC,MAAA,IAAI,UAAa,GAAA,KAAA,CAAM,SAAU,CAAA,CAAA,CAAA,KAAK,MAAM,CAAC,CAAA,CAAA;AAC7C,MAAa,UAAA,GAAA,UAAA,KAAe,CAAK,CAAA,GAAA,KAAA,CAAM,MAAS,GAAA,UAAA,CAAA;AAChD,MAAA,MAAM,aAAgBA,GAAAA,SAAAA,CAAS,CAAC,CAAA,CAAE,OAAO,UAAU,CAAA,CAAA;AACnD,MAAA,IAAI,eAAe,KAAM,CAAA,MAAA,SAAe,CAAC,aAAA,EAAe,SAAS,MAAM,CAAA,CAAA;AAGvE,MAAA,MAAM,YAAe,GAAA,KAAA,CAAM,KAAM,CAAA,UAAU,EAAE,MAAO,CAAA,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,GAAO,MAAO,CAAA,IAAI,GAAG,EAAE,CAAA,CAAA;AAGhG,MAAM,MAAA,SAAA,GAAY,kBAAmB,CAAA,YAAA,EAAcA,SAAQ,CAAA,CAAA;AAE3D,MAAA,OAAO,CAAC,aAAA,GAAgB,SAAW,EAAA,QAAA,CAAS,MAAM,CAAA,CAAA;AAAA,KACtD;AAAA,GACH,CAAA,CAAA;AACL,EAAA;AAWa,IAAA,aAAA,GAAgB,CAACA,SAC1B,KAAA,YAAA,CAAa,gBAAgBA,SAAQ,CAAA,EAAG,eAAgBA,CAAAA,SAAQ,CAAC,EAAA;AAErE,SAAS,sBAAA,CACL,OACA,aACqD,EAAA;AACrD,EAAM,MAAA,CAAC,YAAc,EAAA,SAAS,CAAI,GAAA,KAAA,CAAM,KAAM,CAAA,IAAI,MAAO,CAAA,CAAA,IAAA,EAAO,aAAa,CAAA,IAAA,CAAM,CAAC,CAAA,CAAA;AACpF,EAAO,OAAA,CAAC,cAAc,SAAS,CAAA,CAAA;AACnC,CAAA;AAEA,SAAS,kBAAA,CAAmB,OAAeA,SAA0B,EAAA;AACjE,EAAM,MAAA,IAAA,GAAO,MAAOA,CAAAA,SAAAA,CAAS,MAAM,CAAA,CAAA;AACnC,EAAA,IAAI,GAAM,GAAA,EAAA,CAAA;AACV,EAAA,KAAA,MAAW,QAAQ,KAAO,EAAA;AACtB,IAAO,GAAA,IAAA,IAAA,CAAA;AACP,IAAA,GAAA,IAAO,MAAOA,CAAAA,SAAAA,CAAS,OAAQ,CAAA,IAAI,CAAC,CAAA,CAAA;AAAA,GACxC;AACA,EAAO,OAAA,GAAA,CAAA;AACX,CAAA;AAEA,SAAS,kBAAA,CAAmB,OAAeA,SAA0B,EAAA;AACjE,EAAM,MAAA,IAAA,GAAO,MAAOA,CAAAA,SAAAA,CAAS,MAAM,CAAA,CAAA;AACnC,EAAA,MAAM,YAAY,EAAC,CAAA;AACnB,EAAA,OAAO,QAAQ,EAAI,EAAA;AACf,IAAA,SAAA,CAAU,QAAQA,SAAS,CAAA,MAAA,CAAO,KAAQ,GAAA,IAAI,CAAC,CAAC,CAAA,CAAA;AAChD,IAAS,KAAA,IAAA,IAAA,CAAA;AAAA,GACb;AACA,EAAO,OAAA,SAAA,CAAU,KAAK,EAAE,CAAA,CAAA;AAC5B,CAAA;;;ACtHA,IAAM,QAAW,GAAA,YAAA,CAAA;AAGJ,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgB,QAAQ,EAAA;AAGjD,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgB,QAAQ,EAAA;AAGjD,IAAA,cAAA,GAAiB,MAAM,aAAA,CAAc,QAAQ,EAAA;ACQ1D,IAAM,gCAAmC,GAAA;AAAA,EACrC,QAAU,EAAA,kBAAA;AAAA,EACV,IAAM,EAAA,EAAA;AACV,CAAA,CAAA;AAEA,SAAS,iBAAiB,IAAc,EAAA;AACpC,EAAA,IAAI,IAAQ,IAAA,EAAA,eAAa,IAAQ,IAAA,EAAA,oBAAkB,IAAO,GAAA,EAAA,YAAA;AAC1D,EAAA,IAAI,QAAQ,EAAa,eAAA,IAAA,IAAQ,EAAW,aAAA,OAAO,QAAQ,EAAY,cAAA,EAAA,CAAA,CAAA;AACvE,EAAA,IAAI,QAAQ,EAAa,eAAA,IAAA,IAAQ,GAAW,aAAA,OAAO,QAAQ,EAAY,cAAA,EAAA,CAAA,CAAA;AAC3E,CAAA;AAGa,IAAA,gBAAA,GAAmB,MAC5BC,aAAc,CAAA;AAAA,EACV,kBAAkB,CAAC,KAAA,KAAkB,KAAK,IAAK,CAAA,KAAA,CAAM,SAAS,CAAC,CAAA;AAAA,EAC/D,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,IAAA,MAAM,MAAM,KAAM,CAAA,MAAA,CAAA;AAClB,IAAA,MAAM,KAAK,GAAM,GAAA,CAAA,CAAA;AACjB,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,CAAA,GAAI,KAAM,CAAA,UAAA,CAAW,CAAC,CAAA,CAAA;AAC5B,MAAM,MAAA,CAAA,GAAI,iBAAiB,CAAC,CAAA,CAAA;AAC5B,MAAA,IAAI,MAAM,KAAW,CAAA,EAAA;AACjB,QAAM,MAAA,IAAIC,YAAYC,6CAA+C,EAAA;AAAA,UACjE,GAAG,gCAAA;AAAA,UACH,KAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACL;AACA,MAAA,KAAA,CAAM,GAAI,CAAA,CAAC,CAAC,CAAA,EAAG,MAAM,CAAA,CAAA;AACrB,MAAA,OAAO,CAAI,GAAA,MAAA,CAAA;AAAA,KACf;AACA,IAAM,MAAA,QAAA,GAAW,IAAI,UAAA,CAAW,EAAE,CAAA,CAAA;AAClC,IAAA,KAAA,IAAS,IAAI,CAAG,EAAA,CAAA,GAAI,CAAG,EAAA,CAAA,GAAI,IAAI,CAAK,EAAA,EAAA;AAChC,MAAM,MAAA,EAAA,GAAK,KAAM,CAAA,UAAA,CAAW,CAAG,EAAA,CAAA,CAAA;AAC/B,MAAM,MAAA,EAAA,GAAK,KAAM,CAAA,UAAA,CAAW,CAAG,EAAA,CAAA,CAAA;AAE/B,MAAM,MAAA,EAAA,GAAK,iBAAiB,EAAE,CAAA,CAAA;AAC9B,MAAM,MAAA,EAAA,GAAK,iBAAiB,EAAE,CAAA,CAAA;AAC9B,MAAI,IAAA,EAAA,KAAO,UAAc,EAAO,KAAA,KAAA,CAAA,IAAa,CAAC,MAAO,CAAA,KAAA,CAAM,EAAE,CAAI,EAAA;AAC7D,QAAM,MAAA,IAAID,YAAYC,6CAA+C,EAAA;AAAA,UACjE,GAAG,gCAAA;AAAA,UACH,KAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACL;AACA,MAAS,QAAA,CAAA,CAAC,CAAI,GAAA,CAAC,MAAO,CAAA,KAAA,CAAM,EAAE,CAAK,GAAA,EAAA,IAAM,CAAM,IAAA,EAAA,IAAM,CAAK,CAAA,GAAA,EAAA,CAAA;AAAA,KAC9D;AAEA,IAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA,CAAA;AAC1B,IAAA,OAAO,SAAS,MAAS,GAAA,MAAA,CAAA;AAAA,GAC7B;AACJ,CAAC,EAAA;AAGQ,IAAA,gBAAA,GAAmB,MAC5BC,aAAc,CAAA;AAAA,EACV,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,IAAA,MAAM,QAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,CAAK,SAAS,EAAE,CAAA,CAAE,SAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA,CAAA;AACpG,IAAO,OAAA,CAAC,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,GAC/B;AACJ,CAAC,EAAA;AAGE,IAAM,iBAAiB,MAAiCC,YAAAA,CAAa,gBAAiB,EAAA,EAAG,kBAAkB,EAAA;;;AC9ElH,IAAML,SAAW,GAAA,4DAAA,CAAA;AAGJ,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgBA,SAAQ,EAAA;AAGjD,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgBA,SAAQ,EAAA;AAGjD,IAAA,cAAA,GAAiB,MAAM,aAAA,CAAcA,SAAQ,EAAA;ACInD,IAAM,sBAAyB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACrDC,aAAc,CAAA;AAAA,EACV,gBAAA,EAAkB,CAAC,KAAkB,KAAA,IAAA,CAAK,MAAO,KAAM,CAAA,MAAA,GAAS,OAAQ,CAAC,CAAA;AAAA,EACzE,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,IAAA,qBAAA,CAAsBD,WAAU,KAAK,CAAA,CAAA;AACrC,IAAI,IAAA,KAAA,KAAU,IAAW,OAAA,MAAA,CAAA;AACzB,IAAM,MAAA,WAAA,GAAc,CAAC,GAAG,KAAK,CAAA,CAAE,IAAI,CAAKA,CAAAA,KAAAA,SAAAA,CAAS,OAAQ,CAAA,CAAC,CAAC,CAAA,CAAA;AAC3D,IAAA,MAAM,aAAgB,GAAA,OAAA,CAAQ,WAAa,EAAA,IAAA,EAAM,GAAG,KAAK,CAAA,CAAA;AACzD,IAAM,KAAA,CAAA,GAAA,CAAI,eAAe,MAAM,CAAA,CAAA;AAC/B,IAAA,OAAO,cAAc,MAAS,GAAA,MAAA,CAAA;AAAA,GAClC;AACJ,CAAC,EAAA;AAME,IAAM,sBAAyB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACrDI,aAAc,CAAA;AAAA,EACV,IAAA,CAAK,QAAU,EAAA,MAAA,GAAS,CAAqB,EAAA;AACzC,IAAA,MAAM,QAAQ,MAAW,KAAA,CAAA,GAAI,QAAW,GAAA,QAAA,CAAS,MAAM,MAAM,CAAA,CAAA;AAC7D,IAAA,IAAI,MAAM,MAAW,KAAA,CAAA,SAAU,CAAC,EAAA,EAAI,SAAS,MAAM,CAAA,CAAA;AACnD,IAAM,MAAA,WAAA,GAAc,QAAQ,CAAC,GAAG,KAAK,CAAG,EAAA,CAAA,EAAG,MAAM,IAAI,CAAA,CAAA;AACrD,IAAA,OAAO,CAAC,WAAA,CAAY,GAAI,CAAA,CAAA,CAAA,KAAKJ,SAAS,CAAA,CAAC,CAAC,CAAA,CAAE,IAAK,CAAA,EAAE,CAAG,EAAA,QAAA,CAAS,MAAM,CAAA,CAAA;AAAA,GACvE;AACJ,CAAC,EAAA;AASE,IAAM,oBAAuB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACnDK,YAAa,CAAA,sBAAA,CAAuBL,SAAU,EAAA,IAAI,CAAG,EAAA,sBAAA,CAAuBA,SAAU,EAAA,IAAI,CAAC,EAAA;AAG/F,SAAS,OAAQ,CAAA,KAAA,EAAiB,SAAmB,EAAA,UAAA,EAAoB,YAAiC,EAAA;AACtG,EAAA,MAAM,SAAS,EAAC,CAAA;AAChB,EAAA,IAAI,WAAc,GAAA,CAAA,CAAA;AAClB,EAAA,IAAI,iBAAoB,GAAA,CAAA,CAAA;AACxB,EAAM,MAAA,IAAA,GAAA,CAAQ,KAAK,UAAc,IAAA,CAAA,CAAA;AACjC,EAAA,KAAA,MAAW,SAAS,KAAO,EAAA;AACvB,IAAA,WAAA,GAAe,eAAe,SAAa,GAAA,KAAA,CAAA;AAC3C,IAAqB,iBAAA,IAAA,SAAA,CAAA;AACrB,IAAA,OAAO,qBAAqB,UAAY,EAAA;AACpC,MAAqB,iBAAA,IAAA,UAAA,CAAA;AACrB,MAAO,MAAA,CAAA,IAAA,CAAM,WAAe,IAAA,iBAAA,GAAqB,IAAI,CAAA,CAAA;AAAA,KACzD;AAAA,GACJ;AACA,EAAI,IAAA,YAAA,IAAgB,oBAAoB,CAAG,EAAA;AACvC,IAAA,MAAA,CAAO,IAAM,CAAA,WAAA,IAAgB,UAAa,GAAA,iBAAA,GAAsB,IAAI,CAAA,CAAA;AAAA,GACxE;AACA,EAAO,OAAA,MAAA,CAAA;AACX,CAAA;;;ACvDA,IAAMA,SAAW,GAAA,kEAAA,CAAA;AAGV,IAAM,mBAAmB,MAAmC;AAgC/D,EAAgB;AACZ,IAAA,OAAOC,aAAc,CAAA;AAAA,MACjB,kBAAkB,CAAC,KAAA,KAAkB,OAAO,IAAK,CAAA,KAAA,EAAO,QAAQ,CAAE,CAAA,MAAA;AAAA,MAClE,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,QAAA,qBAAA,CAAsBD,SAAU,EAAA,KAAA,CAAM,OAAQ,CAAA,IAAA,EAAM,EAAE,CAAC,CAAA,CAAA;AACvD,QAAA,MAAM,MAAS,GAAA,MAAA,CAAO,IAAK,CAAA,KAAA,EAAO,QAAQ,CAAA,CAAA;AAC1C,QAAM,KAAA,CAAA,GAAA,CAAI,QAAQ,MAAM,CAAA,CAAA;AACxB,QAAA,OAAO,OAAO,MAAS,GAAA,MAAA,CAAA;AAAA,OAC3B;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AAGJ,EAAA;AAGO,IAAM,mBAAmB,MAAmC;AAW/D,EAAgB;AACZ,IAAA,OAAOI,aAAc,CAAA;AAAA,MACjB,IAAM,EAAA,CAAC,KAAO,EAAA,MAAA,GAAS,MAAM,CAAC,MAAA,CAAO,IAAK,CAAA,KAAA,EAAO,MAAM,CAAE,CAAA,QAAA,CAAS,QAAQ,CAAA,EAAG,MAAM,MAAM,CAAA;AAAA,KAC5F,CAAA,CAAA;AAAA,GACL;AAKJ,EAAA;AAGO,IAAM,iBAAiB,MAAiCC,YAAAA,CAAa,gBAAiB,EAAA,EAAG,kBAAkB,EAAA;;;ACxF3G,IAAM,uBAAuB,CAAC,KAAA;AAAA;AAAA,EAEjC,KAAA,CAAM,OAAQ,CAAA,SAAA,EAAW,EAAE,CAAA;AAAA,EAAA;AAGxB,IAAM,oBAAoB,CAAC,KAAA,EAAe,UAAkB,KAAM,CAAA,MAAA,CAAO,OAAO,IAAQ,EAAA;;;ACNxF,IAAMC,IAAc,UAAW,CAAA,WAAA,CAAA;AAA/B,IACMC,IAAc,UAAW,CAAA,WAAA,CAAA;;;ACY/B,IAAM,iBAAiB,MAAmC;AAC7D,EAAI,IAAA,WAAA,CAAA;AACJ,EAAA,OAAON,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,YAAU,WAAgB,KAAA,IAAI,GAAe,EAAA,MAAA,CAAO,KAAK,CAAE,CAAA,MAAA;AAAA,IAC7E,KAAO,EAAA,CAAC,KAAe,EAAA,KAAA,EAAO,MAAW,KAAA;AACrC,MAAA,MAAM,cAAc,WAAgB,KAAA,IAAI,CAAY,EAAA,EAAG,OAAO,KAAK,CAAA,CAAA;AACnE,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA,CAAA;AAC5B,MAAA,OAAO,SAAS,UAAW,CAAA,MAAA,CAAA;AAAA,KAC/B;AAAA,GACH,CAAA,CAAA;AACL,EAAA;AAGO,IAAM,iBAAiB,MAAmC;AAC7D,EAAI,IAAA,WAAA,CAAA;AACJ,EAAA,OAAOG,aAAc,CAAA;AAAA,IACjB,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,MAAM,MAAA,KAAA,GAAA,CAAS,gBAAgB,IAAI,CAAA,IAAe,MAAO,CAAA,KAAA,CAAM,KAAM,CAAA,MAAM,CAAC,CAAA,CAAA;AAC5E,MAAA,OAAO,CAAC,oBAAA,CAAqB,KAAK,CAAA,EAAG,MAAM,MAAM,CAAA,CAAA;AAAA,KACrD;AAAA,GACH,CAAA,CAAA;AACL,EAAA;AAGO,IAAM,eAAe,MAAiCC,YAAAA,CAAa,cAAe,EAAA,EAAG,gBAAgB", "file": "index.node.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\n/**\n * Asserts that a given string matches a given alphabet.\n */\nexport function assertValidBaseString(alphabet: string, testValue: string, givenValue = testValue) {\n    if (!testValue.match(new RegExp(`^[${alphabet}]*$`))) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n            alphabet,\n            base: alphabet.length,\n            value: givenValue,\n        });\n    }\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXEncoder = (alphabet: string): VariableSizeEncoder<string> => {\n    return createEncoder({\n        getSizeFromValue: (value: string): number => {\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (!tailChars) return value.length;\n\n            const base10Number = getBigIntFromBaseX(tailChars, alphabet);\n            return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);\n        },\n        write(value: string, bytes, offset) {\n            // Check if the value is valid.\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n\n            // Handle leading zeroes.\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (!tailChars) {\n                bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);\n                return offset + leadingZeroes.length;\n            }\n\n            // From baseX to base10.\n            let base10Number = getBigIntFromBaseX(tailChars, alphabet);\n\n            // From base10 to bytes.\n            const tailBytes: number[] = [];\n            while (base10Number > 0n) {\n                tailBytes.unshift(Number(base10Number % 256n));\n                base10Number /= 256n;\n            }\n\n            const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/**\n * Decodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXDecoder = (alphabet: string): VariableSizeDecoder<string> => {\n    return createDecoder({\n        read(rawBytes, offset): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', 0];\n\n            // Handle leading zeroes.\n            let trailIndex = bytes.findIndex(n => n !== 0);\n            trailIndex = trailIndex === -1 ? bytes.length : trailIndex;\n            const leadingZeroes = alphabet[0].repeat(trailIndex);\n            if (trailIndex === bytes.length) return [leadingZeroes, rawBytes.length];\n\n            // From bytes to base10.\n            const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);\n\n            // From base10 to baseX.\n            const tailChars = getBaseXFromBigInt(base10Number, alphabet);\n\n            return [leadingZeroes + tailChars, rawBytes.length];\n        },\n    });\n};\n\n/**\n * A string codec that requires a custom alphabet and uses\n * the length of that alphabet as the base. It then divides\n * the input by the base as many times as necessary to get\n * the output. It also supports leading zeroes by using the\n * first character of the alphabet as the zero character.\n *\n * This can be used to create codecs such as base10 or base58.\n */\nexport const getBaseXCodec = (alphabet: string): VariableSizeCodec<string> =>\n    combineCodec(getBaseXEncoder(alphabet), getBaseXDecoder(alphabet));\n\nfunction partitionLeadingZeroes(\n    value: string,\n    zeroCharacter: string,\n): [leadingZeros: string, tailChars: string | undefined] {\n    const [leadingZeros, tailChars] = value.split(new RegExp(`((?!${zeroCharacter}).*)`));\n    return [leadingZeros, tailChars];\n}\n\nfunction getBigIntFromBaseX(value: string, alphabet: string): bigint {\n    const base = BigInt(alphabet.length);\n    let sum = 0n;\n    for (const char of value) {\n        sum *= base;\n        sum += BigInt(alphabet.indexOf(char));\n    }\n    return sum;\n}\n\nfunction getBaseXFromBigInt(value: bigint, alphabet: string): string {\n    const base = BigInt(alphabet.length);\n    const tailChars = [];\n    while (value > 0n) {\n        tailChars.unshift(alphabet[Number(value % base)]);\n        value /= base;\n    }\n    return tailChars.join('');\n}\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '0123456789';\n\n/** Encodes strings in base10. */\nexport const getBase10Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base10. */\nexport const getBase10Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base10. */\nexport const getBase10Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nconst enum HexC {\n    ZERO = 48, // 0\n    NINE = 57, // 9\n    A_UP = 65, // A\n    F_UP = 70, // F\n    A_LO = 97, // a\n    F_LO = 102, // f\n}\n\nconst INVALID_STRING_ERROR_BASE_CONFIG = {\n    alphabet: '0123456789abcdef',\n    base: 16,\n} as const;\n\nfunction charCodeToBase16(char: number) {\n    if (char >= HexC.ZERO && char <= HexC.NINE) return char - HexC.ZERO;\n    if (char >= HexC.A_UP && char <= HexC.F_UP) return char - (HexC.A_UP - 10);\n    if (char >= HexC.A_LO && char <= HexC.F_LO) return char - (HexC.A_LO - 10);\n}\n\n/** Encodes strings in base16. */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const len = value.length;\n            const al = len / 2;\n            if (len === 1) {\n                const c = value.charCodeAt(0);\n                const n = charCodeToBase16(c);\n                if (n === undefined) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                bytes.set([n], offset);\n                return 1 + offset;\n            }\n            const hexBytes = new Uint8Array(al);\n            for (let i = 0, j = 0; i < al; i++) {\n                const c1 = value.charCodeAt(j++);\n                const c2 = value.charCodeAt(j++);\n\n                const n1 = charCodeToBase16(c1);\n                const n2 = charCodeToBase16(c2);\n                if (n1 === undefined || (n2 === undefined && !Number.isNaN(c2))) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                hexBytes[i] = !Number.isNaN(c2) ? (n1 << 4) | (n2 ?? 0) : n1;\n            }\n\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/** Decodes strings in base16. */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/** Encodes and decodes strings in base16. */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';\n\n/** Encodes strings in base58. */\nexport const getBase58Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base58. */\nexport const getBase58Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base58. */\nexport const getBase58Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecode<PERSON>,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceEncoder = (alphabet: string, bits: number): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.floor((value.length * bits) / 8),\n        write(value: string, bytes, offset) {\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n            const charIndices = [...value].map(c => alphabet.indexOf(c));\n            const reslicedBytes = reslice(charIndices, bits, 8, false);\n            bytes.set(reslicedBytes, offset);\n            return reslicedBytes.length + offset;\n        },\n    });\n\n/**\n * Decodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceDecoder = (alphabet: string, bits: number): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(rawBytes, offset = 0): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', rawBytes.length];\n            const charIndices = reslice([...bytes], 8, bits, true);\n            return [charIndices.map(i => alphabet[i]).join(''), rawBytes.length];\n        },\n    });\n\n/**\n * A string serializer that reslices bytes into custom chunks\n * of bits that are then mapped to a custom alphabet.\n *\n * This can be used to create serializers whose alphabet\n * is a power of 2 such as base16 or base64.\n */\nexport const getBaseXResliceCodec = (alphabet: string, bits: number): VariableSizeCodec<string> =>\n    combineCodec(getBaseXResliceEncoder(alphabet, bits), getBaseXResliceDecoder(alphabet, bits));\n\n/** Helper function to reslice the bits inside bytes. */\nfunction reslice(input: number[], inputBits: number, outputBits: number, useRemainder: boolean): number[] {\n    const output = [];\n    let accumulator = 0;\n    let bitsInAccumulator = 0;\n    const mask = (1 << outputBits) - 1;\n    for (const value of input) {\n        accumulator = (accumulator << inputBits) | value;\n        bitsInAccumulator += inputBits;\n        while (bitsInAccumulator >= outputBits) {\n            bitsInAccumulator -= outputBits;\n            output.push((accumulator >> bitsInAccumulator) & mask);\n        }\n    }\n    if (useRemainder && bitsInAccumulator > 0) {\n        output.push((accumulator << (outputBits - bitsInAccumulator)) & mask);\n    }\n    return output;\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nimport { assertValidBaseString } from './assertions';\nimport { getBaseXResliceDecoder, getBaseXResliceEncoder } from './baseX-reslice';\n\nconst alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n/** Encodes strings in base64. */\nexport const getBase64Encoder = (): VariableSizeEncoder<string> => {\n    if (__BROWSER__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => {\n                try {\n                    return (atob as Window['atob'])(value).length;\n                } catch (e) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        alphabet,\n                        base: 64,\n                        value,\n                    });\n                }\n            },\n            write(value: string, bytes, offset) {\n                try {\n                    const bytesToAdd = (atob as Window['atob'])(value)\n                        .split('')\n                        .map(c => c.charCodeAt(0));\n                    bytes.set(bytesToAdd, offset);\n                    return bytesToAdd.length + offset;\n                } catch (e) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        alphabet,\n                        base: 64,\n                        value,\n                    });\n                }\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => Buffer.from(value, 'base64').length,\n            write(value: string, bytes, offset) {\n                assertValidBaseString(alphabet, value.replace(/=/g, ''));\n                const buffer = Buffer.from(value, 'base64');\n                bytes.set(buffer, offset);\n                return buffer.length + offset;\n            },\n        });\n    }\n\n    return transformEncoder(getBaseXResliceEncoder(alphabet, 6), (value: string): string => value.replace(/=/g, ''));\n};\n\n/** Decodes strings in base64. */\nexport const getBase64Decoder = (): VariableSizeDecoder<string> => {\n    if (__BROWSER__) {\n        return createDecoder({\n            read(bytes, offset = 0) {\n                const slice = bytes.slice(offset);\n                const value = (btoa as Window['btoa'])(String.fromCharCode(...slice));\n                return [value, bytes.length];\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createDecoder({\n            read: (bytes, offset = 0) => [Buffer.from(bytes, offset).toString('base64'), bytes.length],\n        });\n    }\n\n    return transformDecoder(getBaseXResliceDecoder(alphabet, 6), (value: string): string =>\n        value.padEnd(Math.ceil(value.length / 4) * 4, '='),\n    );\n};\n\n/** Encodes and decodes strings in base64. */\nexport const getBase64Codec = (): VariableSizeCodec<string> => combineCodec(getBase64Encoder(), getBase64Decoder());\n", "/**Removes null characters from a string. */\nexport const removeNullCharacters = (value: string) =>\n    // eslint-disable-next-line no-control-regex\n    value.replace(/\\u0000/g, '');\n\n/** Pads a string with null characters at the end. */\nexport const padNullCharacters = (value: string, chars: number) => value.padEnd(chars, '\\u0000');\n", "export const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { TextDecoder, TextEncoder } from '@solana/text-encoding-impl';\n\nimport { removeNullCharacters } from './null-characters';\n\n/** Encodes UTF-8 strings using the native `TextEncoder` API. */\nexport const getUtf8Encoder = (): VariableSizeEncoder<string> => {\n    let textEncoder: TextEncoder;\n    return createEncoder({\n        getSizeFromValue: value => (textEncoder ||= new TextEncoder()).encode(value).length,\n        write: (value: string, bytes, offset) => {\n            const bytesToAdd = (textEncoder ||= new TextEncoder()).encode(value);\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/** Decodes UTF-8 strings using the native `TextDecoder` API. */\nexport const getUtf8Decoder = (): VariableSizeDecoder<string> => {\n    let textDecoder: TextDecoder;\n    return createDecoder({\n        read(bytes, offset) {\n            const value = (textDecoder ||= new TextDecoder()).decode(bytes.slice(offset));\n            return [removeNullCharacters(value), bytes.length];\n        },\n    });\n};\n\n/** Encodes and decodes UTF-8 strings using the native `TextEncoder` and `TextDecoder` API. */\nexport const getUtf8Codec = (): VariableSizeCodec<string> => combineCodec(getUtf8Encoder(), getUtf8Decoder());\n"]}