/** Encodes strings in base10. */
export declare const getBase10Encoder: () => import("@solana/codecs-core").VariableSizeEncoder<string>;
/** Decodes strings in base10. */
export declare const getBase10Decoder: () => import("@solana/codecs-core").VariableSizeDecoder<string>;
/** Encodes and decodes strings in base10. */
export declare const getBase10Codec: () => import("@solana/codecs-core").VariableSizeCodec<string>;
//# sourceMappingURL=base10.d.ts.map