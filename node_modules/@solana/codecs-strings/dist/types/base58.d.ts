/** Encodes strings in base58. */
export declare const getBase58Encoder: () => import("@solana/codecs-core").VariableSizeEncoder<string>;
/** Decodes strings in base58. */
export declare const getBase58Decoder: () => import("@solana/codecs-core").VariableSizeDecoder<string>;
/** Encodes and decodes strings in base58. */
export declare const getBase58Codec: () => import("@solana/codecs-core").VariableSizeCodec<string>;
//# sourceMappingURL=base58.d.ts.map