{"version": 3, "file": "option-codec.d.ts", "sourceRoot": "", "sources": ["../../src/option-codec.ts"], "names": [], "mappings": "AAAA,OAAO,EAEH,KAAK,EAGL,OAAO,EACP,OAAO,EAEP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAEhB,kBAAkB,EAGlB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAa7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAEhC,OAAO,EAAgC,MAAM,EAAE,gBAAgB,EAAc,MAAM,UAAU,CAAC;AAG9F,4CAA4C;AAC5C,MAAM,MAAM,iBAAiB,CAAC,OAAO,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IACzF;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,EAAE,kBAAkB,GAAG,QAAQ,CAAC;IAE1C;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;CAC3B,CAAC;AAEF;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,EACxD,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EACpC,MAAM,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GACjF,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AACpD,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC5E,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACnE,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAChD,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EACpB,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GAC/E,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAwChD;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,EACtD,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,EAClC,MAAM,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GACjF,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACxC,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC5E,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACnE,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAClB,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GAC/E,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAyCpC;;;;;GAKG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EAAE,KAAK,SAAS,MAAM,EACzE,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,EACvC,MAAM,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GAC/E,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/D,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,iBAAiB,CAAC,oBAAoB,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GAC1E,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,EAAE,QAAQ,CAAA;CAAE,GACjE,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,MAAM,CAAC,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG;IAAE,SAAS,CAAC,EAAE,kBAAkB,CAAA;CAAE,GAC7E,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC"}